{"name": "login-dashboard-poc-frontend", "version": "0.0.0", "type": "module", "main": "index.tsx", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@vitejs/plugin-react": "^2.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^4.8.4", "vite": "^3.1.0"}, "devDependencies": {"@types/node": "^17.0.34", "@types/react": "^18.0.34", "@types/react-dom": "^18.0.11", "eslint": "^8.23.1", "eslint-plugin-react": "^7.30.0"}}