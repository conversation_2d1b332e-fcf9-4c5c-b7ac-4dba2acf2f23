"""
Example demonstrating the enhanced domain-driven approach
for AI project generation.

This example shows how the new approach works with a sample
e-commerce project request.
"""

from reapo import (
    ProjectState,
    Entity,
    EntityAttribute,
    EntityRelationship,
    CommonFunction
)

def demonstrate_enhanced_approach():
    """
    Demonstrate the new domain-driven approach with a sample e-commerce project.
    """
    
    print("🚀 Enhanced Domain-Driven Project Generation Demo")
    print("=" * 60)
    
    # Step 1: Sample user query
    user_query = "Create an e-commerce website where users can browse products, add them to cart, and make purchases"
    print(f"\n1. User Query: {user_query}")
    
    # Step 2: Sample requirement analysis (what the AI would generate)
    requirement_analysis = """
    Project Type: E-commerce Web Application
    Core Functionality: Product catalog, shopping cart, order management, user authentication
    User Types: Customers, Administrators
    Key Features: Product browsing, cart management, checkout process, order tracking
    Business Logic: Inventory management, payment processing, order fulfillment
    """
    print(f"\n2. Requirement Analysis:\n{requirement_analysis}")
    
    # Step 3: Sample entities (what the AI would identify)
    entities = [
        Entity(
            name="User",
            description="Represents system users (customers and admins)",
            is_auth_related=True,
            attributes=[
                EntityAttribute(name="id", type="integer", constraints={"primary_key": True}),
                EntityAttribute(name="email", type="string", constraints={"unique": True, "max_length": 255}),
                EntityAttribute(name="password_hash", type="string", constraints={"max_length": 255}),
                EntityAttribute(name="first_name", type="string", constraints={"max_length": 100}),
                EntityAttribute(name="last_name", type="string", constraints={"max_length": 100}),
                EntityAttribute(name="is_admin", type="boolean", required=False),
                EntityAttribute(name="created_at", type="datetime"),
            ]
        ),
        Entity(
            name="Product",
            description="Represents products available for purchase",
            is_auth_related=False,
            attributes=[
                EntityAttribute(name="id", type="integer", constraints={"primary_key": True}),
                EntityAttribute(name="name", type="string", constraints={"max_length": 200}),
                EntityAttribute(name="description", type="text"),
                EntityAttribute(name="price", type="decimal", constraints={"precision": 10, "scale": 2}),
                EntityAttribute(name="stock_quantity", type="integer"),
                EntityAttribute(name="category_id", type="integer"),
                EntityAttribute(name="image_url", type="string", required=False),
                EntityAttribute(name="created_at", type="datetime"),
            ]
        ),
        Entity(
            name="Order",
            description="Represents customer orders",
            is_auth_related=False,
            attributes=[
                EntityAttribute(name="id", type="integer", constraints={"primary_key": True}),
                EntityAttribute(name="user_id", type="integer"),
                EntityAttribute(name="total_amount", type="decimal", constraints={"precision": 10, "scale": 2}),
                EntityAttribute(name="status", type="string", constraints={"max_length": 50}),
                EntityAttribute(name="shipping_address", type="text"),
                EntityAttribute(name="created_at", type="datetime"),
            ],
            relationships=[
                EntityRelationship(
                    from_entity="Order",
                    to_entity="User",
                    relationship_type="many_to_one",
                    foreign_key="user_id",
                    description="Each order belongs to a user"
                )
            ]
        ),
        Entity(
            name="CartItem",
            description="Represents items in user's shopping cart",
            is_auth_related=False,
            attributes=[
                EntityAttribute(name="id", type="integer", constraints={"primary_key": True}),
                EntityAttribute(name="user_id", type="integer"),
                EntityAttribute(name="product_id", type="integer"),
                EntityAttribute(name="quantity", type="integer"),
                EntityAttribute(name="created_at", type="datetime"),
            ],
            relationships=[
                EntityRelationship(
                    from_entity="CartItem",
                    to_entity="User",
                    relationship_type="many_to_one",
                    foreign_key="user_id",
                    description="Cart items belong to a user"
                ),
                EntityRelationship(
                    from_entity="CartItem",
                    to_entity="Product",
                    relationship_type="many_to_one",
                    foreign_key="product_id",
                    description="Cart items reference products"
                )
            ]
        )
    ]
    
    print(f"\n3. Identified Entities ({len(entities)}):")
    for entity in entities:
        auth_marker = " (Auth)" if entity.is_auth_related else ""
        print(f"   - {entity.name}{auth_marker}: {len(entity.attributes)} attributes, {len(entity.relationships)} relationships")
    
    # Step 4: Sample common functions (what the AI would identify)
    common_functions = [
        CommonFunction(
            name="Authentication",
            type="authentication",
            entities_involved=["User"],
            description="Handle user login, registration, and session management",
            implementation_details={
                "methods": ["login", "register", "logout", "refresh_token"],
                "middleware": ["auth_required", "token_validation"],
                "endpoints": ["/auth/login", "/auth/register", "/auth/logout"]
            }
        ),
        CommonFunction(
            name="Product Management",
            type="crud",
            entities_involved=["Product"],
            description="CRUD operations for products",
            implementation_details={
                "methods": ["create_product", "get_products", "update_product", "delete_product"],
                "endpoints": ["/products", "/products/{id}", "/products/search"]
            }
        ),
        CommonFunction(
            name="Cart Management",
            type="business_logic",
            entities_involved=["CartItem", "Product", "User"],
            description="Shopping cart operations",
            implementation_details={
                "methods": ["add_to_cart", "remove_from_cart", "update_quantity", "get_cart"],
                "endpoints": ["/cart", "/cart/add", "/cart/remove"]
            }
        ),
        CommonFunction(
            name="Order Processing",
            type="business_logic",
            entities_involved=["Order", "CartItem", "User"],
            description="Order creation and management",
            implementation_details={
                "methods": ["create_order", "get_orders", "update_order_status"],
                "endpoints": ["/orders", "/orders/{id}", "/orders/checkout"]
            }
        )
    ]
    
    print(f"\n4. Identified Common Functions ({len(common_functions)}):")
    for func in common_functions:
        print(f"   - {func.name} ({func.type}): {func.description}")
    
    # Step 5: Sample project structure (what would be generated)
    print(f"\n5. Generated Project Structure:")
    print("""
    ecommerce-app/
    ├── backend/
    │   ├── app/
    │   │   ├── models/
    │   │   │   ├── __init__.py
    │   │   │   ├── user.py          # User entity model
    │   │   │   ├── product.py       # Product entity model
    │   │   │   ├── order.py         # Order entity model
    │   │   │   └── cart_item.py     # CartItem entity model
    │   │   ├── schemas/
    │   │   │   ├── __init__.py
    │   │   │   ├── user.py          # User DTOs/schemas
    │   │   │   ├── product.py       # Product DTOs/schemas
    │   │   │   ├── order.py         # Order DTOs/schemas
    │   │   │   └── cart_item.py     # CartItem DTOs/schemas
    │   │   ├── repositories/
    │   │   │   ├── __init__.py
    │   │   │   ├── user_repo.py     # User CRUD operations
    │   │   │   ├── product_repo.py  # Product CRUD operations
    │   │   │   ├── order_repo.py    # Order CRUD operations
    │   │   │   └── cart_repo.py     # Cart CRUD operations
    │   │   ├── routes/
    │   │   │   ├── __init__.py
    │   │   │   ├── auth.py          # Authentication endpoints
    │   │   │   ├── products.py      # Product endpoints
    │   │   │   ├── orders.py        # Order endpoints
    │   │   │   └── cart.py          # Cart endpoints
    │   │   ├── middleware/
    │   │   │   ├── __init__.py
    │   │   │   ├── auth.py          # Authentication middleware
    │   │   │   └── validation.py    # Validation middleware
    │   │   └── main.py              # FastAPI application
    │   └── requirements.txt
    ├── frontend/
    │   ├── src/
    │   │   ├── components/
    │   │   │   ├── auth/            # Authentication components
    │   │   │   ├── products/        # Product components
    │   │   │   ├── cart/            # Cart components
    │   │   │   └── orders/          # Order components
    │   │   ├── pages/
    │   │   │   ├── LoginPage.tsx
    │   │   │   ├── ProductsPage.tsx
    │   │   │   ├── CartPage.tsx
    │   │   │   └── OrdersPage.tsx
    │   │   ├── services/
    │   │   │   ├── api.ts           # API client
    │   │   │   ├── auth.ts          # Auth service
    │   │   │   ├── products.ts      # Product service
    │   │   │   └── orders.ts        # Order service
    │   │   ├── types/
    │   │   │   ├── User.ts          # User interfaces
    │   │   │   ├── Product.ts       # Product interfaces
    │   │   │   └── Order.ts         # Order interfaces
    │   │   └── App.tsx
    │   └── package.json
    └── database/
        ├── migrations/
        │   ├── 001_create_users.sql
        │   ├── 002_create_products.sql
        │   ├── 003_create_orders.sql
        │   └── 004_create_cart_items.sql
        └── seeds/
            └── sample_data.sql
    """)
    
    print("\n6. Benefits of This Approach:")
    print("   ✅ Systematic entity-relationship modeling")
    print("   ✅ Consistent code structure across all files")
    print("   ✅ Proper separation of concerns")
    print("   ✅ Scalable architecture")
    print("   ✅ Production-ready code patterns")
    print("   ✅ Automatic relationship handling")
    print("   ✅ Template-based code generation")
    
    print(f"\n🎉 Enhanced approach generates {len(entities)} entities with {len(common_functions)} common functions!")
    print("This results in a much more organized and maintainable codebase.")

if __name__ == "__main__":
    demonstrate_enhanced_approach()
