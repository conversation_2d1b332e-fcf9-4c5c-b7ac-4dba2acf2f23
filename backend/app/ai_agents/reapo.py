import json
import os
from typing import List, Dict, Any

import requests
from langchain.chat_models.base import BaseChatModel
from langchain.schema import (
    AIMessage,
    HumanMessage,
    BaseMessage,
    ChatResult,
    ChatGeneration,
)
from langgraph.graph import StateGraph, END
from pydantic import BaseModel
from pydantic import Field

"""
Enhanced Domain-Driven Agentic Architecture:
1. Requirement Analysis - Parse and understand user intent deeply
2. Entity Identification - Extract business objects/models from requirements
3. Attribute Definition - Define properties, types, and constraints for each entity
4. Relationship Mapping - Identify connections between entities (1:1, 1:many, many:many)
5. Common Function Identification - Authentication, CRUD, validation, middleware
6. Architecture Generation - Create systematic project structure based on entities
7. Template-Based Code Generation - Generate code using entity-driven templates
8. Integration Layer - Ensure all components work together seamlessly
9. Testing & Validation - Generate tests based on entity relationships
10. Error Handling & Refinement Loop
"""


class CustomGroqLLM(BaseChatModel):
    api_key: str = Field(...)
    model_name: str = Field(...)

    def _call_api(self, messages: List[BaseMessage]):
        url = "https://api.groq.com/openai/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        formatted_messages = [
            {
                "role": "user" if isinstance(m, HumanMessage) else "assistant",
                "content": m.content,
            }
            for m in messages
        ]

        data = {"messages": formatted_messages, "model": self.model_name}

        response_data = requests.post(
            url, headers=headers, json=data, verify=False
        ).json()
        print("Full Groq API response:", response_data)
        return response_data["choices"][0]["message"]["content"]

    def _generate(self, messages: List[BaseMessage], **_kwargs) -> ChatResult:
        content = self._call_api(messages)
        ai_message = AIMessage(content=content)
        generation = ChatGeneration(message=ai_message)
        return ChatResult(generations=[generation])

    @property
    def _llm_type(self) -> str:
        return "custom-groq"


custom_llm = CustomGroqLLM(
    api_key="********************************************************",
    model_name="meta-llama/llama-4-scout-17b-16e-instruct",
)


class EntityAttribute(BaseModel):
    name: str
    type: str  # string, integer, boolean, date, etc.
    required: bool = True
    constraints: Dict[str, Any] = {}  # max_length, min_value, etc.
    description: str = ""


class EntityRelationship(BaseModel):
    from_entity: str
    to_entity: str
    relationship_type: str  # one_to_one, one_to_many, many_to_many
    foreign_key: str = ""
    description: str = ""


class Entity(BaseModel):
    name: str
    attributes: List[EntityAttribute] = []
    relationships: List[EntityRelationship] = []
    description: str = ""
    is_auth_related: bool = False


class CommonFunction(BaseModel):
    name: str
    type: str  # authentication, crud, validation, middleware, etc.
    entities_involved: List[str] = []
    description: str = ""
    implementation_details: Dict[str, Any] = {}


class ProjectState(BaseModel):
    # Original fields
    user_query: str = ""
    llm_response: str = ""
    update_required: bool = False
    project_name: str = ""
    project_path: str = ""
    error_query: str = ""
    error_handling_mode: bool = False

    # Enhanced domain-driven fields
    requirement_analysis: str = ""
    entities: List[Entity] = []
    common_functions: List[CommonFunction] = []
    tech_stack: Dict[str, str] = {}  # frontend, backend, database, etc.

    # Generated artifacts
    project_architecture: str = ""
    project_structure_json: Dict[str, Any] = {}
    project_files: List[str] = []  # List of all files in the project
    current_file_index: int = 0  # For tracking progress
    generated_files: Dict[str, str] = {}
    run_commands: List[str] = []


def read_user_query(state: ProjectState) -> ProjectState:
    """Read user query for initial project creation or error handling"""
    try:
        if state.error_handling_mode:
            user_query = input("Describe the error you're facing: ")
            return state.model_copy(update={"error_query": user_query})
        else:
            user_query = input(
                state.llm_response
                if state.llm_response
                else "Tell me what you want to build: "
            )
            return state.model_copy(update={"user_query": user_query})
    except Exception as e:
        print("Error at node read_user_query", e)
        return state.model_copy(update={"user_query": ""})


def analyze_requirements(state: ProjectState) -> ProjectState:
    """Step 1: Deep requirement analysis"""
    try:
        analysis_prompt = f"""
        You are a senior business analyst and software architect. Analyze the following user requirement in detail.

        User Query: "{state.user_query}"

        Your task is to provide a comprehensive requirement analysis including:
        1. Project Type (e.g., e-commerce, blog, dashboard, social media, etc.)
        2. Core Functionality (what the system should do)
        3. User Types (who will use the system)
        4. Key Features (main capabilities needed)
        5. Business Logic (rules and workflows)
        6. Technology Recommendations:
           - Frontend: React with TypeScript (default)
           - Backend: Python FastAPI (default)
           - Database: PostgreSQL (default)
           - Authentication: JWT-based

        Provide a detailed analysis in a structured format.
        """

        llm_response = custom_llm.invoke(analysis_prompt)
        analysis_content = llm_response.content.strip()

        return state.model_copy(
            update={
                "requirement_analysis": analysis_content,
                "llm_response": analysis_content,
                "tech_stack": {
                    "frontend": "React with TypeScript",
                    "backend": "Python FastAPI",
                    "database": "PostgreSQL",
                    "authentication": "JWT-based",
                },
            }
        )

    except Exception as e:
        print("Error at node analyze_requirements", e)
        return state.model_copy(update={"requirement_analysis": ""})


def identify_entities(state: ProjectState) -> ProjectState:
    """Step 2: Identify entities from requirements"""
    try:
        entities_prompt = f"""
        You are a database architect and domain modeling expert. Based on the requirement analysis, identify all the entities (business objects) needed for this system.

        Requirement Analysis:
        {state.requirement_analysis}

        User Query: {state.user_query}

        Your task:
        1. Identify all entities (nouns) that represent business objects
        2. For each entity, determine if it's authentication-related
        3. Provide a brief description of each entity's purpose

        Return the response in this exact JSON format:
        {{
            "entities": [
                {{
                    "name": "User",
                    "description": "Represents system users",
                    "is_auth_related": true
                }},
                {{
                    "name": "Product",
                    "description": "Represents products in the system",
                    "is_auth_related": false
                }}
            ]
        }}

        Only return the JSON, no explanations.
        """

        llm_response = custom_llm.invoke(entities_prompt)
        json_content = llm_response.content.strip()

        # Clean up markdown if present
        if json_content.startswith("```json"):
            json_content = (
                json_content.replace("```json", "").replace("```", "").strip()
            )
        elif json_content.startswith("```"):
            json_content = json_content.replace("```", "").strip()

        try:
            entities_data = json.loads(json_content)
            entities = []

            for entity_data in entities_data.get("entities", []):
                entity = Entity(
                    name=entity_data["name"],
                    description=entity_data.get("description", ""),
                    is_auth_related=entity_data.get("is_auth_related", False),
                )
                entities.append(entity)

            print(f"\n🎯 Identified {len(entities)} entities:")
            for entity in entities:
                auth_marker = " (Auth)" if entity.is_auth_related else ""
                print(f"  - {entity.name}{auth_marker}: {entity.description}")

            return state.model_copy(update={"entities": entities})

        except json.JSONDecodeError as e:
            print(f"JSON parsing error in identify_entities: {e}")
            return state

    except Exception as e:
        print("Error at node identify_entities", e)
        return state


def ask_user_confirmation(state: ProjectState) -> ProjectState:
    print(state.llm_response)
    answer = input("Do you want to update your query? (yes/no): ").strip().lower()
    update_required = answer in ("yes", "y")
    return state.model_copy(update={"update_required": update_required})


def define_attributes_and_relationships(state: ProjectState) -> ProjectState:
    """Step 3 & 4: Define attributes for entities and identify relationships"""
    try:
        if not state.entities:
            print("No entities found to define attributes for")
            return state

        entity_names = [entity.name for entity in state.entities]

        attributes_prompt = f"""
        You are a database design expert. For each entity, define its attributes and relationships.

        Entities identified: {entity_names}
        Requirement Analysis: {state.requirement_analysis}
        User Query: {state.user_query}

        For each entity, define:
        1. Attributes with their types and constraints
        2. Relationships with other entities

        Return response in this exact JSON format:
        {{
            "entities": [
                {{
                    "name": "User",
                    "attributes": [
                        {{
                            "name": "id",
                            "type": "integer",
                            "required": true,
                            "constraints": {{"primary_key": true, "auto_increment": true}},
                            "description": "Unique identifier"
                        }},
                        {{
                            "name": "email",
                            "type": "string",
                            "required": true,
                            "constraints": {{"max_length": 255, "unique": true}},
                            "description": "User email address"
                        }}
                    ],
                    "relationships": [
                        {{
                            "from_entity": "User",
                            "to_entity": "Order",
                            "relationship_type": "one_to_many",
                            "foreign_key": "user_id",
                            "description": "A user can have multiple orders"
                        }}
                    ]
                }}
            ]
        }}

        Only return the JSON, no explanations.
        """

        llm_response = custom_llm.invoke(attributes_prompt)
        json_content = llm_response.content.strip()

        # Clean up markdown if present
        if json_content.startswith("```json"):
            json_content = (
                json_content.replace("```json", "").replace("```", "").strip()
            )
        elif json_content.startswith("```"):
            json_content = json_content.replace("```", "").strip()

        try:
            entities_data = json.loads(json_content)
            updated_entities = []

            for entity_data in entities_data.get("entities", []):
                # Find existing entity or create new one
                existing_entity = next(
                    (e for e in state.entities if e.name == entity_data["name"]), None
                )

                # Parse attributes
                attributes = []
                for attr_data in entity_data.get("attributes", []):
                    attribute = EntityAttribute(
                        name=attr_data["name"],
                        type=attr_data["type"],
                        required=attr_data.get("required", True),
                        constraints=attr_data.get("constraints", {}),
                        description=attr_data.get("description", ""),
                    )
                    attributes.append(attribute)

                # Parse relationships
                relationships = []
                for rel_data in entity_data.get("relationships", []):
                    relationship = EntityRelationship(
                        from_entity=rel_data["from_entity"],
                        to_entity=rel_data["to_entity"],
                        relationship_type=rel_data["relationship_type"],
                        foreign_key=rel_data.get("foreign_key", ""),
                        description=rel_data.get("description", ""),
                    )
                    relationships.append(relationship)

                # Create updated entity
                if existing_entity:
                    updated_entity = existing_entity.model_copy(
                        update={
                            "attributes": attributes,
                            "relationships": relationships,
                        }
                    )
                else:
                    updated_entity = Entity(
                        name=entity_data["name"],
                        attributes=attributes,
                        relationships=relationships,
                        description=entity_data.get("description", ""),
                        is_auth_related=entity_data.get("is_auth_related", False),
                    )

                updated_entities.append(updated_entity)

            print(
                f"\n📋 Defined attributes and relationships for {len(updated_entities)} entities:"
            )
            for entity in updated_entities:
                print(
                    f"  - {entity.name}: {len(entity.attributes)} attributes, {len(entity.relationships)} relationships"
                )

            return state.model_copy(update={"entities": updated_entities})

        except json.JSONDecodeError as e:
            print(f"JSON parsing error in define_attributes_and_relationships: {e}")
            return state

    except Exception as e:
        print("Error at node define_attributes_and_relationships", e)
        return state


def identify_common_functions(state: ProjectState) -> ProjectState:
    """Step 5: Identify common functions like authentication, CRUD operations, etc."""
    try:
        if not state.entities:
            print("No entities found to identify common functions for")
            return state

        entity_names = [entity.name for entity in state.entities]
        auth_entities = [
            entity.name for entity in state.entities if entity.is_auth_related
        ]

        functions_prompt = f"""
        You are a software architect. Based on the entities and requirements, identify common functions needed.

        Entities: {entity_names}
        Auth-related entities: {auth_entities}
        Requirement Analysis: {state.requirement_analysis}
        Tech Stack: {state.tech_stack}

        Identify common functions needed such as:
        1. Authentication (login, register, logout, password reset)
        2. CRUD operations for each entity
        3. Validation functions
        4. Middleware (auth, logging, error handling)
        5. File upload/download
        6. Email notifications
        7. Search and filtering
        8. Pagination

        Return response in this exact JSON format:
        {{
            "common_functions": [
                {{
                    "name": "Authentication",
                    "type": "authentication",
                    "entities_involved": ["User"],
                    "description": "Handle user login, registration, and session management",
                    "implementation_details": {{
                        "methods": ["login", "register", "logout", "refresh_token"],
                        "middleware": ["auth_required", "token_validation"],
                        "endpoints": ["/auth/login", "/auth/register", "/auth/logout"]
                    }}
                }},
                {{
                    "name": "User CRUD",
                    "type": "crud",
                    "entities_involved": ["User"],
                    "description": "Create, read, update, delete operations for User entity",
                    "implementation_details": {{
                        "methods": ["create_user", "get_user", "update_user", "delete_user"],
                        "endpoints": ["/users", "/users/{{id}}"]
                    }}
                }}
            ]
        }}

        Only return the JSON, no explanations.
        """

        llm_response = custom_llm.invoke(functions_prompt)
        json_content = llm_response.content.strip()

        # Clean up markdown if present
        if json_content.startswith("```json"):
            json_content = (
                json_content.replace("```json", "").replace("```", "").strip()
            )
        elif json_content.startswith("```"):
            json_content = json_content.replace("```", "").strip()

        try:
            functions_data = json.loads(json_content)
            common_functions = []

            for func_data in functions_data.get("common_functions", []):
                common_function = CommonFunction(
                    name=func_data["name"],
                    type=func_data["type"],
                    entities_involved=func_data.get("entities_involved", []),
                    description=func_data.get("description", ""),
                    implementation_details=func_data.get("implementation_details", {}),
                )
                common_functions.append(common_function)

            print(f"\n🔧 Identified {len(common_functions)} common functions:")
            for func in common_functions:
                print(f"  - {func.name} ({func.type}): {func.description}")

            return state.model_copy(update={"common_functions": common_functions})

        except json.JSONDecodeError as e:
            print(f"JSON parsing error in identify_common_functions: {e}")
            return state

    except Exception as e:
        print("Error at node identify_common_functions", e)
        return state


def check_update_needed(state: ProjectState) -> str:
    return "read_user_query" if state.update_required else "identify_entities"


def generate_entity_driven_project_structure(state: ProjectState) -> ProjectState:
    """Step 6: Generate project structure based on entities and common functions"""
    try:
        if not state.entities or not state.common_functions:
            print("Missing entities or common functions for structure generation")
            return state

        # Prepare entity and function information
        entities_info = []
        for entity in state.entities:
            entity_info = {
                "name": entity.name,
                "attributes": [
                    {"name": attr.name, "type": attr.type} for attr in entity.attributes
                ],
                "is_auth_related": entity.is_auth_related,
            }
            entities_info.append(entity_info)

        functions_info = []
        for func in state.common_functions:
            func_info = {
                "name": func.name,
                "type": func.type,
                "entities_involved": func.entities_involved,
            }
            functions_info.append(func_info)

        structure_prompt = f"""
        You are a software architect. Generate a complete project structure based on the domain analysis.

        Entities: {json.dumps(entities_info, indent=2)}
        Common Functions: {json.dumps(functions_info, indent=2)}
        Tech Stack: {state.tech_stack}
        User Query: {state.user_query}

        Generate a production-ready project structure that includes:
        1. Backend structure (FastAPI):
           - Models for each entity
           - Schemas/DTOs for each entity
           - CRUD repositories for each entity
           - API routes for each entity
           - Authentication middleware and routes
           - Database configuration
           - Main application file

        2. Frontend structure (React + TypeScript):
           - Components for each entity
           - Pages for CRUD operations
           - Authentication components
           - API service files
           - Types/interfaces for each entity
           - Routing configuration

        3. Database:
           - Migration files for each entity
           - Seed data files

        Return response in this exact JSON format:
        {{
            "project_name": "entity-driven-app",
            "structure": {{
                "backend": {{
                    "type": "folder",
                    "children": {{
                        "app": {{
                            "type": "folder",
                            "children": {{
                                "models": {{
                                    "type": "folder",
                                    "children": {{
                                        "__init__.py": {{"type": "file"}},
                                        "user.py": {{"type": "file"}}
                                    }}
                                }},
                                "schemas": {{
                                    "type": "folder",
                                    "children": {{
                                        "__init__.py": {{"type": "file"}},
                                        "user.py": {{"type": "file"}}
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}

        Only return the JSON, no explanations.
        """

        llm_response = custom_llm.invoke(structure_prompt)
        json_content = llm_response.content.strip()

        # Clean up markdown if present
        if json_content.startswith("```json"):
            json_content = (
                json_content.replace("```json", "").replace("```", "").strip()
            )
        elif json_content.startswith("```"):
            json_content = json_content.replace("```", "").strip()

        try:
            project_json = json.loads(json_content)
            project_name = project_json.get("project_name", "entity-driven-app")

            print(f"\n🏗️ Generated entity-driven project structure for: {project_name}")

            return state.model_copy(
                update={
                    "project_architecture": json_content,
                    "project_structure_json": project_json,
                    "project_name": project_name,
                }
            )
        except json.JSONDecodeError as e:
            print(f"JSON parsing error: {e}")
            print(f"Raw content: {json_content}")
            return state.model_copy(update={"project_architecture": json_content})

    except Exception as e:
        print("Error at node generate_entity_driven_project_structure", e)
        return state.model_copy(update={"project_architecture": ""})


def create_project_structure_from_json(state: ProjectState) -> ProjectState:
    """Create files and folders by reading JSON structure"""
    try:
        if not state.project_structure_json:
            print("No valid JSON structure found")
            return state

        project_name = state.project_structure_json.get("project_name", "my-web-app")
        structure = state.project_structure_json.get("structure", {})

        # Set project path in Generated-projects folder (user preference)
        generated_projects_dir = os.path.join(os.getcwd(), "Generated-projects")
        os.makedirs(generated_projects_dir, exist_ok=True)
        project_root = os.path.join(generated_projects_dir, project_name)
        state = state.model_copy(update={"project_path": project_root})

        # Create project root directory
        os.makedirs(project_root, exist_ok=True)

        project_files = []

        def create_structure(items: dict, current_path: str):
            """Recursively create structure from JSON"""
            for name, details in items.items():
                item_path = os.path.join(current_path, name)

                if details.get("type") == "folder":
                    os.makedirs(item_path, exist_ok=True)
                    print(
                        f" Created folder: {os.path.relpath(item_path, project_root)}"
                    )

                    # Recursively create children
                    children = details.get("children", {})
                    if children:
                        create_structure(children, item_path)

                elif details.get("type") == "file":
                    # Ensure parent directory exists
                    os.makedirs(os.path.dirname(item_path), exist_ok=True)

                    # Create empty file
                    with open(item_path, "w", encoding="utf-8") as f:
                        f.write("")

                    # Add to project files list
                    relative_path = os.path.relpath(item_path, project_root)
                    project_files.append(relative_path)
                    print(f" Created file: {relative_path}")

        # Create the structure
        create_structure(structure, project_root)

        print(f"\nProject structure created successfully!")
        print(f" Project: {project_name}")
        print(f" Location: {project_root}")
        print(f" Total files: {len(project_files)}")

        return state.model_copy(update={"project_files": project_files})

    except Exception as e:
        print(f"Error creating project structure: {e}")
        return state


def scan_project_files(state: ProjectState) -> ProjectState:
    """Scan the created project directory to get all files that need code generation"""
    try:
        project_files = []

        # Debug: Check if project path exists
        if not os.path.exists(state.project_path):
            print(f"⚠️ Project path does not exist: {state.project_path}")
            return state.model_copy(update={"project_files": []})

        print(f"🔍 Scanning project directory: {state.project_path}")

        for root, _, files in os.walk(state.project_path):
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, state.project_path)

                # Skip certain files that don't need code generation
                skip_extensions = [
                    ".png",
                    ".jpg",
                    ".jpeg",
                    ".gif",
                    ".svg",
                    ".ico",
                    ".pdf",
                    ".zip",
                ]
                skip_files = ["README.md", ".gitignore", ".env.example"]

                if (
                    not any(file.endswith(ext) for ext in skip_extensions)
                    and file not in skip_files
                ):
                    project_files.append(relative_path)

        print(f"\n🔍 Found {len(project_files)} files to generate code for:")
        for file in project_files:
            print(f"  - {file}")

        return state.model_copy(
            update={"project_files": project_files, "current_file_index": 0}
        )

    except Exception as e:
        print(f"Error scanning project files: {e}")
        # Return empty list instead of trying to access potentially missing attribute
        return state.model_copy(update={"project_files": []})


def generate_backend_code(state: ProjectState) -> ProjectState:
    """Generate backend code based on entities and common functions"""
    try:
        if not state.entities or not state.common_functions:
            print("Missing entities or common functions for backend generation")
            return state

        print(f"\n🔧 Generating Backend Code...")

        # Filter backend files
        backend_files = [f for f in state.project_files if f.startswith("backend/")]

        if not backend_files:
            print("No backend files found in project structure")
            return state

        generated_files = state.generated_files.copy()

        # Prepare context for backend generation
        entities_context = []
        for entity in state.entities:
            entity_context = {
                "name": entity.name,
                "attributes": [
                    {
                        "name": attr.name,
                        "type": attr.type,
                        "required": attr.required,
                        "constraints": attr.constraints,
                    }
                    for attr in entity.attributes
                ],
                "relationships": [
                    {
                        "from": rel.from_entity,
                        "to": rel.to_entity,
                        "type": rel.relationship_type,
                        "fk": rel.foreign_key,
                    }
                    for rel in entity.relationships
                ],
                "is_auth_related": entity.is_auth_related,
                "description": entity.description,
            }
            entities_context.append(entity_context)

        functions_context = []
        for func in state.common_functions:
            func_context = {
                "name": func.name,
                "type": func.type,
                "entities_involved": func.entities_involved,
                "description": func.description,
                "implementation_details": func.implementation_details,
            }
            functions_context.append(func_context)

        for i, current_file in enumerate(backend_files):
            print(
                f"\n[{i + 1}/{len(backend_files)}] Generating Backend: {current_file}"
            )

            # Generate backend-specific code
            backend_prompt = f"""
            You are an expert backend developer specializing in Python FastAPI. Generate production-ready backend code.

            Project Context:
            - User Query: {state.user_query}
            - Project Name: {state.project_name}
            - Tech Stack: {state.tech_stack}
            - Entities: {json.dumps(entities_context, indent=2)}
            - Common Functions: {json.dumps(functions_context, indent=2)}

            Current Backend File: {current_file}

            Requirements:
            1. Generate complete, production-ready Python/FastAPI code
            2. Use the entity definitions to create proper models, schemas, and repositories
            3. Implement proper authentication and authorization
            4. Include error handling, validation, and logging
            5. Follow FastAPI best practices and patterns
            6. Ensure proper database relationships and constraints
            7. Include proper imports and dependencies
            8. Make the code modular and maintainable
            9. Add comprehensive docstrings and comments

            For different file types:
            - Models: Use SQLAlchemy ORM with proper relationships
            - Schemas: Use Pydantic models for request/response validation
            - Routes: Implement RESTful API endpoints with proper HTTP methods
            - Repositories: Implement CRUD operations with error handling
            - Auth: Implement JWT-based authentication with proper security

            Generate ONLY the code content for {current_file}:
            """

            try:
                llm_response = custom_llm.invoke(backend_prompt)
                code_content = llm_response.content.strip()

                # Clean up markdown if present
                if code_content.startswith("```"):
                    lines = code_content.split("\n")
                    start_idx = 1
                    while start_idx < len(lines) and lines[start_idx].strip() == "":
                        start_idx += 1

                    end_idx = len(lines) - 1
                    while end_idx > 0 and not lines[end_idx].strip().startswith("```"):
                        end_idx -= 1

                    if end_idx > start_idx:
                        code_content = "\n".join(lines[start_idx:end_idx])
                    else:
                        code_content = "\n".join(lines[1:-1])

                # Store the generated code
                generated_files[current_file] = code_content

                # Write the code to file immediately
                file_path = os.path.join(state.project_path, current_file)
                os.makedirs(os.path.dirname(file_path), exist_ok=True)

                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(code_content)

                print(f"✅ Generated backend: {current_file}")

            except Exception as e:
                print(f"❌ Error generating {current_file}: {e}")
                # Create placeholder file
                file_path = os.path.join(state.project_path, current_file)
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(f"# TODO: Generate backend code for {current_file}\n")

        print(
            f"\n✅ Backend code generation completed! Generated {len(backend_files)} files."
        )

        return state.model_copy(update={"generated_files": generated_files})

    except Exception as e:
        print(f"Error in generate_backend_code: {e}")
        return state


def generate_frontend_code(state: ProjectState) -> ProjectState:
    """Generate frontend code based on entities and common functions"""
    try:
        if not state.entities or not state.common_functions:
            print("Missing entities or common functions for frontend generation")
            return state

        print(f"\n🎨 Generating Frontend Code...")

        # Filter frontend files
        frontend_files = [f for f in state.project_files if f.startswith("frontend/")]

        if not frontend_files:
            print("No frontend files found in project structure")
            return state

        generated_files = state.generated_files.copy()

        # Prepare context for frontend generation
        entities_context = []
        for entity in state.entities:
            entity_context = {
                "name": entity.name,
                "attributes": [
                    {"name": attr.name, "type": attr.type, "required": attr.required}
                    for attr in entity.attributes
                ],
                "relationships": [
                    {
                        "from": rel.from_entity,
                        "to": rel.to_entity,
                        "type": rel.relationship_type,
                    }
                    for rel in entity.relationships
                ],
                "is_auth_related": entity.is_auth_related,
                "description": entity.description,
            }
            entities_context.append(entity_context)

        functions_context = []
        for func in state.common_functions:
            func_context = {
                "name": func.name,
                "type": func.type,
                "entities_involved": func.entities_involved,
                "description": func.description,
                "implementation_details": func.implementation_details,
            }
            functions_context.append(func_context)

        for i, current_file in enumerate(frontend_files):
            print(
                f"\n[{i + 1}/{len(frontend_files)}] Generating Frontend: {current_file}"
            )

            # Generate frontend-specific code
            frontend_prompt = f"""
            You are an expert frontend developer specializing in React with TypeScript. Generate production-ready frontend code.

            Project Context:
            - User Query: {state.user_query}
            - Project Name: {state.project_name}
            - Tech Stack: {state.tech_stack}
            - Entities: {json.dumps(entities_context, indent=2)}
            - Common Functions: {json.dumps(functions_context, indent=2)}

            Current Frontend File: {current_file}

            Requirements:
            1. Generate complete, production-ready React/TypeScript code
            2. Use the entity definitions to create proper components and interfaces
            3. Implement proper state management and API integration
            4. Include error handling, loading states, and user feedback
            5. Follow React best practices and patterns
            6. Ensure proper TypeScript typing and interfaces
            7. Include proper imports and dependencies
            8. Make components reusable and maintainable
            9. Add comprehensive JSDoc comments
            10. Implement responsive design principles

            For different file types:
            - Components: Create functional components with proper props and state
            - Pages: Implement full page components with routing
            - Services: Create API service functions with proper error handling
            - Types: Define TypeScript interfaces matching backend entities
            - Hooks: Create custom hooks for state management and API calls

            Generate ONLY the code content for {current_file}:
            """

            try:
                llm_response = custom_llm.invoke(frontend_prompt)
                code_content = llm_response.content.strip()

                # Clean up markdown if present
                if code_content.startswith("```"):
                    lines = code_content.split("\n")
                    start_idx = 1
                    while start_idx < len(lines) and lines[start_idx].strip() == "":
                        start_idx += 1

                    end_idx = len(lines) - 1
                    while end_idx > 0 and not lines[end_idx].strip().startswith("```"):
                        end_idx -= 1

                    if end_idx > start_idx:
                        code_content = "\n".join(lines[start_idx:end_idx])
                    else:
                        code_content = "\n".join(lines[1:-1])

                # Store the generated code
                generated_files[current_file] = code_content

                # Write the code to file immediately
                file_path = os.path.join(state.project_path, current_file)
                os.makedirs(os.path.dirname(file_path), exist_ok=True)

                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(code_content)

                print(f"✅ Generated frontend: {current_file}")

            except Exception as e:
                print(f"❌ Error generating {current_file}: {e}")
                # Create placeholder file
                file_path = os.path.join(state.project_path, current_file)
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(f"// TODO: Generate frontend code for {current_file}\n")

        print(
            f"\n✅ Frontend code generation completed! Generated {len(frontend_files)} files."
        )

        return state.model_copy(update={"generated_files": generated_files})

    except Exception as e:
        print(f"Error in generate_frontend_code: {e}")
        return state


def generate_run_commands(state: ProjectState) -> ProjectState:
    """Generate all commands needed to run the entire project"""
    try:
        commands_prompt = f"""
        Based on the following project that was just created:
        
        Project Name: {state.project_name}
        User Query: {state.user_query}
        Project Structure: {json.dumps(state.project_structure_json, indent=2)}
        Generated Files: {list(state.generated_files.keys())}
        
        Generate a comprehensive list of commands to run this project from scratch.
        Include:
        1. Installation commands (npm install, pip install, etc.)
        2. Environment setup commands
        3. Database setup commands (if applicable)
        4. Build commands
        5. Run/Start commands for both frontend and backend
        6. Any other necessary setup steps
        Format the response as a numbered list of commands with brief explanations.
        Make sure the commands are practical and executable.
        """

        llm_response = custom_llm.invoke(commands_prompt)
        commands_content = llm_response.content.strip()

        # Extract commands from the response
        command_lines = [
            line.strip() for line in commands_content.split("\n") if line.strip()
        ]

        print(f"\n Run Commands Generated:")
        print("=" * 50)
        print(commands_content)
        print("=" * 50)

        return state.model_copy(
            update={"run_commands": command_lines, "llm_response": commands_content}
        )

    except Exception as e:
        print(f"Error generating run commands: {e}")
        return state


def ask_for_error_handling(state: ProjectState) -> ProjectState:
    """Ask user if they want to report any errors or issues"""
    print(f"\n Project '{state.project_name}' has been created successfully!")
    print(f" Location: {state.project_path}")
    print(f"\n💡 You can now run the project using the commands shown above.")

    answer = (
        input("\nDid you encounter any errors while running the project? (yes/no): ")
        .strip()
        .lower()
    )
    error_mode = answer in ("yes", "y")

    return state.model_copy(update={"error_handling_mode": error_mode})


def check_error_handling_needed(state: ProjectState) -> str:
    """Check if error handling is needed"""
    if state.error_handling_mode:
        return "handle_project_errors"
    else:
        return "finalize_project"


def handle_project_errors(state: ProjectState) -> ProjectState:
    """Handle project errors by analyzing and fixing files"""
    try:
        if not state.error_query:
            # Read error query if not already set
            error_query = input("Please describe the error you're facing: ")
            state = state.model_copy(update={"error_query": error_query})

        print(f"\n Analyzing error: {state.error_query}")

        # Analyze the error and identify which files need to be fixed
        analysis_prompt = f"""
        You are a debugging expert. Analyze the following error and identify which files need to be modified.
        
        Project Context:
        - Project Name: {state.project_name}
        - Project Structure: {json.dumps(state.project_structure_json, indent=2)}
        - Available Files: {state.project_files}
        
        Error Description:
        {state.error_query}
        
        Your task:
        1. Identify which specific files are likely causing the error
        2. List the files that need to be modified (use exact file paths from the project)
        3. Provide a brief explanation of what needs to be fixed in each file
        
        Format your response as:
        FILES_TO_FIX: file1.js, file2.py, file3.tsx
        EXPLANATION: Brief explanation of what needs to be fixed in each file
        """

        analysis_response = custom_llm.invoke(analysis_prompt)
        analysis_content = analysis_response.content.strip()

        print(f"Analysis Result:")
        print(analysis_content)

        # Extract files to fix
        files_to_fix = []
        for line in analysis_content.split("\n"):
            if line.startswith("FILES_TO_FIX:"):
                files_str = line.replace("FILES_TO_FIX:", "").strip()
                files_to_fix = [f.strip() for f in files_str.split(",") if f.strip()]
                break

        if not files_to_fix:
            print(
                " Could not identify specific files to fix. Please provide more details."
            )
            return state

        print(f"\n Fixing {len(files_to_fix)} files...")

        # Fix each identified file
        for file_path in files_to_fix:
            if file_path in state.project_files:
                print(f"\n Fixing: {file_path}")

                # Read current file content
                full_file_path = os.path.join(state.project_path, file_path)
                current_content = ""
                if os.path.exists(full_file_path):
                    with open(full_file_path, "r", encoding="utf-8") as f:
                        current_content = f.read()

                # Generate fixed code
                fix_prompt = f"""
                You are an expert developer. Fix the following file to resolve the reported error.
                
                Error Description: {state.error_query}
                File to Fix: {file_path}
                Current File Content:
                {current_content}
                
                Project Context:
                - Project Name: {state.project_name}
                - Project Structure: {json.dumps(state.project_structure_json, indent=2)}
                
                Requirements:
                1. Fix the specific error mentioned
                2. Maintain the existing functionality
                3. Ensure the code follows best practices
                4. Make sure all imports and dependencies are correct
                5. Add error handling if needed
                
                Generate the complete fixed code for {file_path}:
                """

                try:
                    fix_response = custom_llm.invoke(fix_prompt)
                    fixed_code = fix_response.content.strip()

                    # Clean up markdown if present
                    if fixed_code.startswith("```"):
                        lines = fixed_code.split("\n")
                        start_idx = 1
                        while start_idx < len(lines) and lines[start_idx].strip() == "":
                            start_idx += 1

                        end_idx = len(lines) - 1
                        while end_idx > 0 and not lines[end_idx].strip().startswith(
                            "```"
                        ):
                            end_idx -= 1

                        if end_idx > start_idx:
                            fixed_code = "\n".join(lines[start_idx:end_idx])

                    # Write fixed code to file
                    with open(full_file_path, "w", encoding="utf-8") as f:
                        f.write(fixed_code)

                    print(f"Fixed: {file_path}")

                except Exception as e:
                    print(f" Error fixing {file_path}: {e}")
            else:
                print(f"  File not found in project: {file_path}")

        print(f"\n Error handling completed!")

        # Ask if there are more errors
        another_error = (
            input("\nDo you have any other errors to fix? (yes/no): ").strip().lower()
        )
        if another_error in ("yes", "y"):
            return state.model_copy(
                update={"error_query": "", "error_handling_mode": True}
            )
        else:
            return state.model_copy(update={"error_handling_mode": False})

    except Exception as e:
        print(f"Error in handle_project_errors: {e}")
        return state


def finalize_project(state: ProjectState) -> ProjectState:
    """Provide final summary"""
    try:
        # Safely get project files count
        total_files = (
            len(state.project_files)
            if hasattr(state, "project_files") and state.project_files
            else 0
        )
        generated_files = len(state.generated_files) if state.generated_files else 0

        print(f"\n✅ Project Generation Complete!")
        print(f"📁 Project: {state.project_name}")
        print(f"📍 Location: {state.project_path}")
        print(f"📊 Files Generated: {generated_files}/{total_files}")

        if state.generated_files:
            print(f"\n📋 Generated Files:")
            for file_name in state.generated_files.keys():
                print(f"  ✓ {file_name}")
        else:
            print(f"\n⚠️ No files were generated")

        # Show entities and functions summary
        if hasattr(state, "entities") and state.entities:
            print(f"\n🎯 Entities: {len(state.entities)}")
            for entity in state.entities:
                print(f"  - {entity.name}: {len(entity.attributes)} attributes")

        if hasattr(state, "common_functions") and state.common_functions:
            print(f"\n🔧 Common Functions: {len(state.common_functions)}")
            for func in state.common_functions:
                print(f"  - {func.name} ({func.type})")

        return state.model_copy(
            update={"llm_response": "Project generation completed successfully!"}
        )

    except Exception as e:
        print(f"❌ Error finalizing project: {e}")
        print(f"Debug info:")
        print(f"  - Project name: {getattr(state, 'project_name', 'Unknown')}")
        print(f"  - Project path: {getattr(state, 'project_path', 'Unknown')}")
        print(f"  - Has project_files: {hasattr(state, 'project_files')}")
        print(f"  - Has generated_files: {hasattr(state, 'generated_files')}")
        return state


def print_state(state: ProjectState):
    print(f"\n📊 Final Project State:")
    print(f"📁 Project Name: {getattr(state, 'project_name', 'Unknown')}")

    # Safely get project files count
    project_files_count = (
        len(state.project_files)
        if hasattr(state, "project_files") and state.project_files
        else 0
    )
    print(f"📄 Project Files: {project_files_count} files")

    # Safely get generated files count
    generated_files_count = (
        len(state.generated_files)
        if hasattr(state, "generated_files") and state.generated_files
        else 0
    )
    print(f"✅ Generated Files: {generated_files_count} files")

    print(f"🔧 Error Handling Mode: {getattr(state, 'error_handling_mode', False)}")

    # Show additional info if available
    if hasattr(state, "entities") and state.entities:
        print(f"🎯 Entities: {len(state.entities)}")

    if hasattr(state, "common_functions") and state.common_functions:
        print(f"⚙️ Common Functions: {len(state.common_functions)}")

    if hasattr(state, "tech_stack") and state.tech_stack:
        print(f"🛠️ Tech Stack: {', '.join(state.tech_stack.values())}")


# Build enhanced domain-driven workflow
builder = StateGraph(ProjectState)

# Add nodes for the new domain-driven approach with separate backend/frontend generation
builder.add_node("read_user_query", read_user_query)
builder.add_node("analyze_requirements", analyze_requirements)
builder.add_node("ask_user_confirmation", ask_user_confirmation)
builder.add_node("identify_entities", identify_entities)
builder.add_node(
    "define_attributes_and_relationships", define_attributes_and_relationships
)
builder.add_node("identify_common_functions", identify_common_functions)
builder.add_node(
    "generate_entity_driven_project_structure", generate_entity_driven_project_structure
)
builder.add_node(
    "create_project_structure_from_json", create_project_structure_from_json
)
builder.add_node("scan_project_files", scan_project_files)
builder.add_node("generate_backend_code", generate_backend_code)
builder.add_node("generate_frontend_code", generate_frontend_code)
builder.add_node("generate_run_commands", generate_run_commands)
builder.add_node("ask_for_error_handling", ask_for_error_handling)
builder.add_node("handle_project_errors", handle_project_errors)
builder.add_node("finalize_project", finalize_project)
builder.add_node("print_state", print_state)

# Add edges for the enhanced workflow
builder.set_entry_point("read_user_query")
builder.add_edge("read_user_query", "analyze_requirements")
builder.add_edge("analyze_requirements", "ask_user_confirmation")
builder.add_conditional_edges(
    "ask_user_confirmation",
    check_update_needed,
    {
        "read_user_query": "read_user_query",
        "identify_entities": "identify_entities",
    },
)
builder.add_edge("identify_entities", "define_attributes_and_relationships")
builder.add_edge("define_attributes_and_relationships", "identify_common_functions")
builder.add_edge(
    "identify_common_functions", "generate_entity_driven_project_structure"
)
builder.add_edge(
    "generate_entity_driven_project_structure", "create_project_structure_from_json"
)
builder.add_edge("create_project_structure_from_json", "scan_project_files")
builder.add_edge("scan_project_files", "generate_backend_code")
builder.add_edge("generate_backend_code", "generate_frontend_code")
builder.add_edge("generate_frontend_code", "generate_run_commands")
builder.add_edge("generate_run_commands", "ask_for_error_handling")
builder.add_conditional_edges(
    "ask_for_error_handling",
    check_error_handling_needed,
    {
        "handle_project_errors": "handle_project_errors",
        "finalize_project": "finalize_project",
    },
)
builder.add_conditional_edges(
    "handle_project_errors",
    check_error_handling_needed,
    {
        "handle_project_errors": "handle_project_errors",
        "finalize_project": "finalize_project",
    },
)
builder.add_edge("finalize_project", "print_state")
builder.add_edge("print_state", END)

# Compile and run workflow
workflow = builder.compile()

# Add recursion limit configuration to prevent errors
config = {"recursion_limit": 100}
workflow.invoke({}, config=config)
