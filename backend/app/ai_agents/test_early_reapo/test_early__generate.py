# test_reapo.py

import pytest
from unittest.mock import patch, MagicMock
from langchain.schema import AI<PERSON>essage, ChatResult, ChatGeneration, BaseMessage
from app.ai_agents.reapo import CustomGroqLLM


@pytest.fixture
def llm():
    """Fixture to create a CustomGroqLLM instance with dummy credentials."""
    return CustomGroqLLM(api_key="dummy", model_name="dummy-model")


@pytest.fixture
def base_message():
    """Fixture to create a simple BaseMessage mock."""
    msg = MagicMock(spec=BaseMessage)
    return msg


class TestCustomGroqLLMGenerate:
    """Unit tests for CustomGroqLLM._generate method."""

    @pytest.mark.happy_path
    def test_generate_returns_chatresult_with_expected_content(self, llm, base_message):
        """
        Test that _generate returns a ChatResult containing the expected AIMessage content
        when _call_api returns a normal string.
        """
        expected_content = "Hello, this is a test response."
        with patch.object(llm, "_call_api", return_value=expected_content) as mock_call:
            messages = [base_message]
            result = llm._generate(messages)
            assert isinstance(result, ChatResult)
            assert len(result.generations) == 1
            generation = result.generations[0]
            assert isinstance(generation, ChatGeneration)
            assert isinstance(generation.message, AIMessage)
            assert generation.message.content == expected_content
            mock_call.assert_called_once_with(messages)

    @pytest.mark.happy_path
    def test_generate_with_multiple_messages(self, llm):
        """
        Test that _generate works with multiple BaseMessage objects in the input list.
        """
        messages = [MagicMock(spec=BaseMessage) for _ in range(3)]
        expected_content = "Multi-message response"
        with patch.object(llm, "_call_api", return_value=expected_content):
            result = llm._generate(messages)
            assert isinstance(result, ChatResult)
            assert result.generations[0].message.content == expected_content

    @pytest.mark.happy_path
    def test_generate_with_empty_messages(self, llm):
        """
        Test that _generate works when given an empty list of messages.
        """
        messages = []
        expected_content = "Empty input response"
        with patch.object(llm, "_call_api", return_value=expected_content):
            result = llm._generate(messages)
            assert isinstance(result, ChatResult)
            assert result.generations[0].message.content == expected_content

    @pytest.mark.edge_case
    def test_generate_when_call_api_returns_empty_string(self, llm, base_message):
        """
        Test that _generate handles the case where _call_api returns an empty string.
        """
        with patch.object(llm, "_call_api", return_value=""):
            result = llm._generate([base_message])
            assert isinstance(result, ChatResult)
            assert result.generations[0].message.content == ""

    @pytest.mark.edge_case
    def test_generate_when_call_api_returns_none(self, llm, base_message):
        """
        Test that _generate handles the case where _call_api returns None.
        """
        with patch.object(llm, "_call_api", return_value=None):
            result = llm._generate([base_message])
            assert isinstance(result, ChatResult)
            assert result.generations[0].message.content is None

    @pytest.mark.edge_case
    def test_generate_when_call_api_raises_exception(self, llm, base_message):
        """
        Test that _generate propagates exceptions raised by _call_api.
        """
        with patch.object(llm, "_call_api", side_effect=RuntimeError("API failure")):
            with pytest.raises(RuntimeError, match="API failure"):
                llm._generate([base_message])

    @pytest.mark.edge_case
    def test_generate_with_non_string_content(self, llm, base_message):
        """
        Test that _generate handles the case where _call_api returns a non-string (e.g., dict).
        """
        non_string_content = {"key": "value"}
        with patch.object(llm, "_call_api", return_value=non_string_content):
            result = llm._generate([base_message])
            assert isinstance(result, ChatResult)
            assert result.generations[0].message.content == non_string_content

    @pytest.mark.edge_case
    def test_generate_with_kwargs_passed(self, llm, base_message):
        """
        Test that _generate accepts and passes through additional kwargs.
        """
        with patch.object(
            llm, "_call_api", return_value="test with kwargs"
        ) as mock_call:
            result = llm._generate([base_message], temperature=0.7, max_tokens=10)
            assert isinstance(result, ChatResult)
            assert result.generations[0].message.content == "test with kwargs"
            # kwargs are not used in _call_api, but should not cause errors
            mock_call.assert_called_once_with([base_message])
