"""
Test script to verify the fix for the 'project_files' attribute error.
"""

from reapo import (
    ProjectState,
    Entity,
    EntityAttribute,
    CommonFunction,
    finalize_project,
    print_state
)

def test_finalize_project_fix():
    """
    Test that finalize_project works correctly with the new ProjectState structure.
    """
    
    print("🧪 Testing finalize_project fix...")
    
    # Test 1: Complete state with all attributes
    print("\n1. Testing with complete state:")
    complete_state = ProjectState(
        user_query="Create a simple blog app",
        project_name="test-blog-app",
        project_path="/tmp/test-blog",
        entities=[
            Entity(
                name="Post",
                attributes=[
                    EntityAttribute(name="id", type="integer"),
                    EntityAttribute(name="title", type="string"),
                    EntityAttribute(name="content", type="text"),
                ]
            )
        ],
        common_functions=[
            CommonFunction(
                name="Post CRUD",
                type="crud",
                entities_involved=["Post"],
                description="CRUD operations for posts"
            )
        ],
        project_files=["backend/app/models/post.py", "frontend/src/components/PostList.tsx"],
        generated_files={
            "backend/app/models/post.py": "# Post model code",
            "frontend/src/components/PostList.tsx": "// PostList component"
        },
        tech_stack={"backend": "FastAPI", "frontend": "React"}
    )
    
    try:
        result_state = finalize_project(complete_state)
        print("✅ Complete state test passed")
    except Exception as e:
        print(f"❌ Complete state test failed: {e}")
    
    # Test 2: Minimal state (what might cause the original error)
    print("\n2. Testing with minimal state:")
    minimal_state = ProjectState(
        user_query="Create an app",
        project_name="minimal-app",
        project_path="/tmp/minimal"
    )
    
    try:
        result_state = finalize_project(minimal_state)
        print("✅ Minimal state test passed")
    except Exception as e:
        print(f"❌ Minimal state test failed: {e}")
    
    # Test 3: State with empty lists
    print("\n3. Testing with empty lists:")
    empty_state = ProjectState(
        user_query="Create an empty app",
        project_name="empty-app",
        project_path="/tmp/empty",
        entities=[],
        common_functions=[],
        project_files=[],
        generated_files={},
        tech_stack={}
    )
    
    try:
        result_state = finalize_project(empty_state)
        print("✅ Empty state test passed")
    except Exception as e:
        print(f"❌ Empty state test failed: {e}")
    
    # Test 4: Test print_state function
    print("\n4. Testing print_state function:")
    try:
        print_state(complete_state)
        print("✅ print_state test passed")
    except Exception as e:
        print(f"❌ print_state test failed: {e}")
    
    print("\n🎉 All tests completed!")

def test_project_state_structure():
    """
    Test that the ProjectState structure is correct.
    """
    
    print("\n🔍 Testing ProjectState structure...")
    
    # Create a new ProjectState instance
    state = ProjectState()
    
    # Check that all required attributes exist
    required_attributes = [
        'user_query', 'llm_response', 'update_required', 'project_name', 
        'project_path', 'error_query', 'error_handling_mode',
        'requirement_analysis', 'entities', 'common_functions', 'tech_stack',
        'project_architecture', 'project_structure_json', 'project_files',
        'current_file_index', 'generated_files', 'run_commands'
    ]
    
    missing_attributes = []
    for attr in required_attributes:
        if not hasattr(state, attr):
            missing_attributes.append(attr)
    
    if missing_attributes:
        print(f"❌ Missing attributes: {missing_attributes}")
    else:
        print("✅ All required attributes present")
    
    # Check default values
    print(f"📋 Default values:")
    print(f"  - project_files: {state.project_files} (type: {type(state.project_files)})")
    print(f"  - generated_files: {state.generated_files} (type: {type(state.generated_files)})")
    print(f"  - entities: {state.entities} (type: {type(state.entities)})")
    print(f"  - common_functions: {state.common_functions} (type: {type(state.common_functions)})")

if __name__ == "__main__":
    test_project_state_structure()
    test_finalize_project_fix()
