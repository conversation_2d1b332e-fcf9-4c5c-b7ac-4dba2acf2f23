"""
Test script to demonstrate separate backend and frontend code generation.
This shows how the new approach works with dedicated nodes for each technology stack.
"""

from reapo import (
    ProjectState,
    Entity,
    EntityAttribute,
    EntityRelationship,
    CommonFunction,
    generate_backend_code,
    generate_frontend_code
)

def test_separate_code_generation():
    """
    Test the separate backend and frontend code generation nodes.
    """
    
    print("🧪 Testing Separate Backend & Frontend Code Generation")
    print("=" * 60)
    
    # Create a sample project state with entities and functions
    sample_entities = [
        Entity(
            name="User",
            description="User entity for authentication",
            is_auth_related=True,
            attributes=[
                EntityAttribute(name="id", type="integer", constraints={"primary_key": True}),
                EntityAttribute(name="email", type="string", constraints={"unique": True}),
                EntityAttribute(name="password_hash", type="string"),
                EntityAttribute(name="first_name", type="string"),
                EntityAttribute(name="last_name", type="string"),
            ]
        ),
        Entity(
            name="Product",
            description="Product entity for e-commerce",
            is_auth_related=False,
            attributes=[
                EntityAttribute(name="id", type="integer", constraints={"primary_key": True}),
                EntityAttribute(name="name", type="string"),
                EntityAttribute(name="price", type="decimal"),
                EntityAttribute(name="description", type="text"),
            ]
        )
    ]
    
    sample_functions = [
        CommonFunction(
            name="Authentication",
            type="authentication",
            entities_involved=["User"],
            description="User authentication system",
            implementation_details={
                "endpoints": ["/auth/login", "/auth/register"],
                "methods": ["login", "register", "logout"]
            }
        ),
        CommonFunction(
            name="Product CRUD",
            type="crud",
            entities_involved=["Product"],
            description="Product management operations",
            implementation_details={
                "endpoints": ["/products", "/products/{id}"],
                "methods": ["create", "read", "update", "delete"]
            }
        )
    ]
    
    # Sample project files (what would be generated by structure generation)
    sample_project_files = [
        # Backend files
        "backend/app/models/__init__.py",
        "backend/app/models/user.py",
        "backend/app/models/product.py",
        "backend/app/schemas/__init__.py",
        "backend/app/schemas/user.py",
        "backend/app/schemas/product.py",
        "backend/app/routes/__init__.py",
        "backend/app/routes/auth.py",
        "backend/app/routes/products.py",
        "backend/app/repositories/__init__.py",
        "backend/app/repositories/user_repo.py",
        "backend/app/repositories/product_repo.py",
        "backend/app/main.py",
        "backend/requirements.txt",
        
        # Frontend files
        "frontend/src/components/auth/LoginForm.tsx",
        "frontend/src/components/auth/RegisterForm.tsx",
        "frontend/src/components/products/ProductList.tsx",
        "frontend/src/components/products/ProductCard.tsx",
        "frontend/src/pages/LoginPage.tsx",
        "frontend/src/pages/ProductsPage.tsx",
        "frontend/src/services/api.ts",
        "frontend/src/services/auth.ts",
        "frontend/src/services/products.ts",
        "frontend/src/types/User.ts",
        "frontend/src/types/Product.ts",
        "frontend/src/App.tsx",
        "frontend/package.json",
        
        # Database files
        "database/migrations/001_create_users.sql",
        "database/migrations/002_create_products.sql",
    ]
    
    # Create project state
    project_state = ProjectState(
        user_query="Create an e-commerce app with user authentication and product management",
        project_name="test-ecommerce-app",
        project_path="/tmp/test-project",
        entities=sample_entities,
        common_functions=sample_functions,
        project_files=sample_project_files,
        tech_stack={
            "frontend": "React with TypeScript",
            "backend": "Python FastAPI",
            "database": "PostgreSQL"
        }
    )
    
    print(f"📋 Project Setup:")
    print(f"   - Entities: {len(sample_entities)}")
    print(f"   - Functions: {len(sample_functions)}")
    print(f"   - Total Files: {len(sample_project_files)}")
    
    # Filter files by type
    backend_files = [f for f in sample_project_files if f.startswith('backend/')]
    frontend_files = [f for f in sample_project_files if f.startswith('frontend/')]
    database_files = [f for f in sample_project_files if f.startswith('database/')]
    
    print(f"   - Backend Files: {len(backend_files)}")
    print(f"   - Frontend Files: {len(frontend_files)}")
    print(f"   - Database Files: {len(database_files)}")
    
    print(f"\n🔧 Backend Code Generation Node:")
    print(f"   Processing {len(backend_files)} backend files...")
    for file in backend_files[:5]:  # Show first 5
        print(f"   - {file}")
    if len(backend_files) > 5:
        print(f"   ... and {len(backend_files) - 5} more")
    
    print(f"\n   Backend Generation Features:")
    print(f"   ✅ SQLAlchemy ORM models with relationships")
    print(f"   ✅ Pydantic schemas for validation")
    print(f"   ✅ FastAPI routes with proper HTTP methods")
    print(f"   ✅ CRUD repositories with error handling")
    print(f"   ✅ JWT authentication implementation")
    print(f"   ✅ Database constraints and relationships")
    
    print(f"\n🎨 Frontend Code Generation Node:")
    print(f"   Processing {len(frontend_files)} frontend files...")
    for file in frontend_files[:5]:  # Show first 5
        print(f"   - {file}")
    if len(frontend_files) > 5:
        print(f"   ... and {len(frontend_files) - 5} more")
    
    print(f"\n   Frontend Generation Features:")
    print(f"   ✅ React functional components with TypeScript")
    print(f"   ✅ TypeScript interfaces matching backend entities")
    print(f"   ✅ API service functions with error handling")
    print(f"   ✅ Custom hooks for state management")
    print(f"   ✅ Responsive design components")
    print(f"   ✅ Loading states and user feedback")
    
    print(f"\n🔄 Execution Flow:")
    print(f"   1. Domain Analysis → Entities & Functions identified")
    print(f"   2. Project Structure → Files and folders created")
    print(f"   3. Backend Generation → {len(backend_files)} backend files processed")
    print(f"   4. Frontend Generation → {len(frontend_files)} frontend files processed")
    print(f"   5. Run Commands → Setup and execution instructions")
    
    print(f"\n📁 Project Structure (Same Folder, Separate Processing):")
    print(f"""
    test-ecommerce-app/
    ├── backend/              ← 🔧 Backend Node
    │   ├── app/
    │   │   ├── models/       ← SQLAlchemy models
    │   │   ├── schemas/      ← Pydantic schemas
    │   │   ├── routes/       ← FastAPI routes
    │   │   └── repositories/ ← CRUD operations
    │   └── requirements.txt
    ├── frontend/             ← 🎨 Frontend Node
    │   ├── src/
    │   │   ├── components/   ← React components
    │   │   ├── pages/        ← Page components
    │   │   ├── services/     ← API services
    │   │   └── types/        ← TypeScript types
    │   └── package.json
    └── database/
        └── migrations/       ← SQL migrations
    """)
    
    print(f"\n✅ Benefits of Separate Nodes:")
    print(f"   🎯 Better Specialization - Each node focuses on its technology")
    print(f"   🔧 Improved Code Quality - Technology-specific best practices")
    print(f"   🚀 Enhanced Control - Independent processing and error handling")
    print(f"   📈 Scalability - Easy to add new technology stacks")
    print(f"   🛠️ Maintainability - Clear separation of generation logic")
    
    print(f"\n🚀 Future Enhancements:")
    print(f"   - Database Migration Node for SQL generation")
    print(f"   - Testing Node for unit/integration tests")
    print(f"   - Documentation Node for API docs")
    print(f"   - Mobile App Node for React Native/Flutter")
    print(f"   - Deployment Node for Docker/CI-CD")
    
    print(f"\n🎉 Separate Backend & Frontend Code Generation Ready!")
    print(f"   This approach provides better control and specialization")
    print(f"   while maintaining the same project folder structure.")

if __name__ == "__main__":
    test_separate_code_generation()
