import React, { useState, useEffect } from 'react';
import './App.css';
import { LoginForm } from './components/LoginForm';
import { Dashboard } from './components/Dashboard';
import { useAuth } from './hooks/useAuth';

function App() {
  const { isLoggedIn, logout } = useAuth();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(false);
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="app">
      {isLoggedIn ? (
        <Dashboard logout={logout} />
      ) : (
        <LoginForm />
      )}
    </div>
  );
}

export default App;