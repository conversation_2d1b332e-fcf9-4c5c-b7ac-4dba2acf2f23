{"extends": "../../tsconfig.json", "compilerOptions": {"target": "es6", "module": "esnext", "moduleResolution": "node", "outDir": "build", "sourceMap": true, "noImplicitAny": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strict": true, "strictPropertyInitialization": true, "strictFunctionTypes": true, "strictNullChecks": true, "resolveJsonModule": true, "lib": ["es2015", "dom"], "jsx": "react-jsx", "noFallthroughCasesInSwitch": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"]}