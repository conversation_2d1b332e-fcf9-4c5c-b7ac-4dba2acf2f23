version: '3.9'

services:
  backend:
    build: ./backend
    environment:
      - MONGO_URI=mongodb://mongodb:27017/
      - PORT=8000
    ports:
      - "8000:8000"
    depends_on:
      - mongodb
    restart: always

  frontend:
    build: ./frontend
    ports:
      - "5173:5173"
    depends_on:
      - backend
    restart: always

  mongodb:
    image: mongo:latest
    volumes:
      - mongo-data:/data/db
    ports:
      - "27017:27017"
    restart: always

volumes:
  mongo-data: