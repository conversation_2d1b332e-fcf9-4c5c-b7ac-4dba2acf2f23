"use client";
var Vt=Object.create;var K=Object.defineProperty;var Qt=Object.getOwnPropertyDescriptor;var Wt=Object.getOwnPropertyNames;var Gt=Object.getPrototypeOf,qt=Object.prototype.hasOwnProperty;var Kt=(t,o)=>{for(var e in o)K(t,e,{get:o[e],enumerable:!0})},Ct=(t,o,e,n)=>{if(o&&typeof o=="object"||typeof o=="function")for(let a of Wt(o))!qt.call(t,a)&&a!==e&&K(t,a,{get:()=>o[a],enumerable:!(n=Qt(o,a))||n.enumerable});return t};var w=(t,o,e)=>(e=t!=null?Vt(Gt(t)):{},Ct(o||!t||!t.__esModule?K(e,"default",{value:t,enumerable:!0}):e,t)),Yt=t=>Ct(K({},"__esModule",{value:!0}),t);var uo={};Kt(uo,{Bounce:()=>lt,Flip:()=>Xt,Icons:()=>G,Slide:()=>Ut,ToastContainer:()=>bt,Zoom:()=>Ht,collapseToast:()=>Y,cssTransition:()=>F,toast:()=>y});module.exports=Yt(uo);function jt(t){if(!t||typeof document=="undefined")return;let o=document.head||document.getElementsByTagName("head")[0],e=document.createElement("style");e.type="text/css",o.firstChild?o.insertBefore(e,o.firstChild):o.appendChild(e),e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}jt(`:root{--toastify-color-light: #fff;--toastify-color-dark: #121212;--toastify-color-info: #3498db;--toastify-color-success: #07bc0c;--toastify-color-warning: #f1c40f;--toastify-color-error: hsl(6, 78%, 57%);--toastify-color-transparent: rgba(255, 255, 255, .7);--toastify-icon-color-info: var(--toastify-color-info);--toastify-icon-color-success: var(--toastify-color-success);--toastify-icon-color-warning: var(--toastify-color-warning);--toastify-icon-color-error: var(--toastify-color-error);--toastify-container-width: fit-content;--toastify-toast-width: 320px;--toastify-toast-offset: 16px;--toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));--toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));--toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));--toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));--toastify-toast-background: #fff;--toastify-toast-padding: 14px;--toastify-toast-min-height: 64px;--toastify-toast-max-height: 800px;--toastify-toast-bd-radius: 6px;--toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, .1);--toastify-font-family: sans-serif;--toastify-z-index: 9999;--toastify-text-color-light: #757575;--toastify-text-color-dark: #fff;--toastify-text-color-info: #fff;--toastify-text-color-success: #fff;--toastify-text-color-warning: #fff;--toastify-text-color-error: #fff;--toastify-spinner-color: #616161;--toastify-spinner-color-empty-area: #e0e0e0;--toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);--toastify-color-progress-dark: #bb86fc;--toastify-color-progress-info: var(--toastify-color-info);--toastify-color-progress-success: var(--toastify-color-success);--toastify-color-progress-warning: var(--toastify-color-warning);--toastify-color-progress-error: var(--toastify-color-error);--toastify-color-progress-bgo: .2}.Toastify__toast-container{z-index:var(--toastify-z-index);-webkit-transform:translate3d(0,0,var(--toastify-z-index));position:fixed;width:var(--toastify-container-width);box-sizing:border-box;color:#fff;display:flex;flex-direction:column}.Toastify__toast-container--top-left{top:var(--toastify-toast-top);left:var(--toastify-toast-left)}.Toastify__toast-container--top-center{top:var(--toastify-toast-top);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--top-right{top:var(--toastify-toast-top);right:var(--toastify-toast-right);align-items:end}.Toastify__toast-container--bottom-left{bottom:var(--toastify-toast-bottom);left:var(--toastify-toast-left)}.Toastify__toast-container--bottom-center{bottom:var(--toastify-toast-bottom);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--bottom-right{bottom:var(--toastify-toast-bottom);right:var(--toastify-toast-right);align-items:end}.Toastify__toast{--y: 0;position:relative;touch-action:none;width:var(--toastify-toast-width);min-height:var(--toastify-toast-min-height);box-sizing:border-box;margin-bottom:1rem;padding:var(--toastify-toast-padding);border-radius:var(--toastify-toast-bd-radius);box-shadow:var(--toastify-toast-shadow);max-height:var(--toastify-toast-max-height);font-family:var(--toastify-font-family);z-index:0;display:flex;flex:1 auto;align-items:center;word-break:break-word}@media only screen and (max-width: 480px){.Toastify__toast-container{width:100vw;left:env(safe-area-inset-left);margin:0}.Toastify__toast-container--top-left,.Toastify__toast-container--top-center,.Toastify__toast-container--top-right{top:env(safe-area-inset-top);transform:translate(0)}.Toastify__toast-container--bottom-left,.Toastify__toast-container--bottom-center,.Toastify__toast-container--bottom-right{bottom:env(safe-area-inset-bottom);transform:translate(0)}.Toastify__toast-container--rtl{right:env(safe-area-inset-right);left:initial}.Toastify__toast{--toastify-toast-width: 100%;margin-bottom:0;border-radius:0}}.Toastify__toast-container[data-stacked=true]{width:var(--toastify-toast-width)}.Toastify__toast--stacked{position:absolute;width:100%;transform:translate3d(0,var(--y),0) scale(var(--s));transition:transform .3s}.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body,.Toastify__toast--stacked[data-collapsed] .Toastify__close-button{transition:opacity .1s}.Toastify__toast--stacked[data-collapsed=false]{overflow:visible}.Toastify__toast--stacked[data-collapsed=true]:not(:last-child)>*{opacity:0}.Toastify__toast--stacked:after{content:"";position:absolute;left:0;right:0;height:calc(var(--g) * 1px);bottom:100%}.Toastify__toast--stacked[data-pos=top]{top:0}.Toastify__toast--stacked[data-pos=bot]{bottom:0}.Toastify__toast--stacked[data-pos=bot].Toastify__toast--stacked:before{transform-origin:top}.Toastify__toast--stacked[data-pos=top].Toastify__toast--stacked:before{transform-origin:bottom}.Toastify__toast--stacked:before{content:"";position:absolute;left:0;right:0;bottom:0;height:100%;transform:scaleY(3);z-index:-1}.Toastify__toast--rtl{direction:rtl}.Toastify__toast--close-on-click{cursor:pointer}.Toastify__toast-icon{margin-inline-end:10px;width:22px;flex-shrink:0;display:flex}.Toastify--animate{animation-fill-mode:both;animation-duration:.5s}.Toastify--animate-icon{animation-fill-mode:both;animation-duration:.3s}.Toastify__toast-theme--dark{background:var(--toastify-color-dark);color:var(--toastify-text-color-dark)}.Toastify__toast-theme--light,.Toastify__toast-theme--colored.Toastify__toast--default{background:var(--toastify-color-light);color:var(--toastify-text-color-light)}.Toastify__toast-theme--colored.Toastify__toast--info{color:var(--toastify-text-color-info);background:var(--toastify-color-info)}.Toastify__toast-theme--colored.Toastify__toast--success{color:var(--toastify-text-color-success);background:var(--toastify-color-success)}.Toastify__toast-theme--colored.Toastify__toast--warning{color:var(--toastify-text-color-warning);background:var(--toastify-color-warning)}.Toastify__toast-theme--colored.Toastify__toast--error{color:var(--toastify-text-color-error);background:var(--toastify-color-error)}.Toastify__progress-bar-theme--light{background:var(--toastify-color-progress-light)}.Toastify__progress-bar-theme--dark{background:var(--toastify-color-progress-dark)}.Toastify__progress-bar--info{background:var(--toastify-color-progress-info)}.Toastify__progress-bar--success{background:var(--toastify-color-progress-success)}.Toastify__progress-bar--warning{background:var(--toastify-color-progress-warning)}.Toastify__progress-bar--error{background:var(--toastify-color-progress-error)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error{background:var(--toastify-color-transparent)}.Toastify__close-button{color:#fff;position:absolute;top:6px;right:6px;background:transparent;outline:none;border:none;padding:0;cursor:pointer;opacity:.7;transition:.3s ease;z-index:1}.Toastify__toast--rtl .Toastify__close-button{left:6px;right:unset}.Toastify__close-button--light{color:#000;opacity:.3}.Toastify__close-button>svg{fill:currentColor;height:16px;width:14px}.Toastify__close-button:hover,.Toastify__close-button:focus{opacity:1}@keyframes Toastify__trackProgress{0%{transform:scaleX(1)}to{transform:scaleX(0)}}.Toastify__progress-bar{position:absolute;bottom:0;left:0;width:100%;height:100%;z-index:1;opacity:.7;transform-origin:left}.Toastify__progress-bar--animated{animation:Toastify__trackProgress linear 1 forwards}.Toastify__progress-bar--controlled{transition:transform .2s}.Toastify__progress-bar--rtl{right:0;left:initial;transform-origin:right;border-bottom-left-radius:initial}.Toastify__progress-bar--wrp{position:absolute;overflow:hidden;bottom:0;left:0;width:100%;height:5px;border-bottom-left-radius:var(--toastify-toast-bd-radius);border-bottom-right-radius:var(--toastify-toast-bd-radius)}.Toastify__progress-bar--wrp[data-hidden=true]{opacity:0}.Toastify__progress-bar--bg{opacity:var(--toastify-color-progress-bgo);width:100%;height:100%}.Toastify__spinner{width:20px;height:20px;box-sizing:border-box;border:2px solid;border-radius:100%;border-color:var(--toastify-spinner-color-empty-area);border-right-color:var(--toastify-spinner-color);animation:Toastify__spin .65s linear infinite}@keyframes Toastify__bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutRight{20%{opacity:1;transform:translate3d(-20px,var(--y),0)}to{opacity:0;transform:translate3d(2000px,var(--y),0)}}@keyframes Toastify__bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutLeft{20%{opacity:1;transform:translate3d(20px,var(--y),0)}to{opacity:0;transform:translate3d(-2000px,var(--y),0)}}@keyframes Toastify__bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}@keyframes Toastify__bounceOutUp{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}@keyframes Toastify__bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}@keyframes Toastify__bounceOutDown{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.Toastify__bounce-enter--top-left,.Toastify__bounce-enter--bottom-left{animation-name:Toastify__bounceInLeft}.Toastify__bounce-enter--top-right,.Toastify__bounce-enter--bottom-right{animation-name:Toastify__bounceInRight}.Toastify__bounce-enter--top-center{animation-name:Toastify__bounceInDown}.Toastify__bounce-enter--bottom-center{animation-name:Toastify__bounceInUp}.Toastify__bounce-exit--top-left,.Toastify__bounce-exit--bottom-left{animation-name:Toastify__bounceOutLeft}.Toastify__bounce-exit--top-right,.Toastify__bounce-exit--bottom-right{animation-name:Toastify__bounceOutRight}.Toastify__bounce-exit--top-center{animation-name:Toastify__bounceOutUp}.Toastify__bounce-exit--bottom-center{animation-name:Toastify__bounceOutDown}@keyframes Toastify__zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes Toastify__zoomOut{0%{opacity:1}50%{opacity:0;transform:translate3d(0,var(--y),0) scale3d(.3,.3,.3)}to{opacity:0}}.Toastify__zoom-enter{animation-name:Toastify__zoomIn}.Toastify__zoom-exit{animation-name:Toastify__zoomOut}@keyframes Toastify__flipIn{0%{transform:perspective(400px) rotateX(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateX(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateX(10deg);opacity:1}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}@keyframes Toastify__flipOut{0%{transform:translate3d(0,var(--y),0) perspective(400px)}30%{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(-20deg);opacity:1}to{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(90deg);opacity:0}}.Toastify__flip-enter{animation-name:Toastify__flipIn}.Toastify__flip-exit{animation-name:Toastify__flipOut}@keyframes Toastify__slideInRight{0%{transform:translate3d(110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInLeft{0%{transform:translate3d(-110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInUp{0%{transform:translate3d(0,110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInDown{0%{transform:translate3d(0,-110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideOutRight{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(110%,var(--y),0)}}@keyframes Toastify__slideOutLeft{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(-110%,var(--y),0)}}@keyframes Toastify__slideOutDown{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,500px,0)}}@keyframes Toastify__slideOutUp{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,-500px,0)}}.Toastify__slide-enter--top-left,.Toastify__slide-enter--bottom-left{animation-name:Toastify__slideInLeft}.Toastify__slide-enter--top-right,.Toastify__slide-enter--bottom-right{animation-name:Toastify__slideInRight}.Toastify__slide-enter--top-center{animation-name:Toastify__slideInDown}.Toastify__slide-enter--bottom-center{animation-name:Toastify__slideInUp}.Toastify__slide-exit--top-left,.Toastify__slide-exit--bottom-left{animation-name:Toastify__slideOutLeft;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-right,.Toastify__slide-exit--bottom-right{animation-name:Toastify__slideOutRight;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-center{animation-name:Toastify__slideOutUp;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--bottom-center{animation-name:Toastify__slideOutDown;animation-timing-function:ease-in;animation-duration:.3s}@keyframes Toastify__spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}
`);var xt=require("react"),z=t=>typeof t=="number"&&!isNaN(t),D=t=>typeof t=="string",P=t=>typeof t=="function",Et=t=>D(t)||z(t),X=t=>D(t)||P(t)?t:null,Pt=(t,o)=>t===!1||z(t)&&t>0?t:o,V=t=>(0,xt.isValidElement)(t)||D(t)||P(t)||z(t);var M=w(require("react"));function Y(t,o,e=300){let{scrollHeight:n,style:a}=t;requestAnimationFrame(()=>{a.minHeight="initial",a.height=n+"px",a.transition=`all ${e}ms`,requestAnimationFrame(()=>{a.height="0",a.padding="0",a.margin="0",setTimeout(o,e)})})}function F({enter:t,exit:o,appendPosition:e=!1,collapse:n=!0,collapseDuration:a=300}){return function({children:s,position:d,preventExitTransition:c,done:T,nodeRef:g,isIn:v,playToast:x}){let C=e?`${t}--${d}`:t,k=e?`${o}--${d}`:o,E=(0,M.useRef)(0);return(0,M.useLayoutEffect)(()=>{let f=g.current,p=C.split(" "),b=r=>{r.target===g.current&&(x(),f.removeEventListener("animationend",b),f.removeEventListener("animationcancel",b),E.current===0&&r.type!=="animationcancel"&&f.classList.remove(...p))};(()=>{f.classList.add(...p),f.addEventListener("animationend",b),f.addEventListener("animationcancel",b)})()},[]),(0,M.useEffect)(()=>{let f=g.current,p=()=>{f.removeEventListener("animationend",p),n?Y(f,T,a):T()};v||(c?p():(()=>{E.current=1,f.className+=` ${k}`,f.addEventListener("animationend",p)})())},[v]),M.default.createElement(M.default.Fragment,null,s)}}var j=require("react");function dt(t,o){return{content:mt(t.content,t.props),containerId:t.props.containerId,id:t.props.toastId,theme:t.props.theme,type:t.props.type,data:t.props.data||{},isLoading:t.props.isLoading,icon:t.props.icon,reason:t.removalReason,status:o}}function mt(t,o,e=!1){return(0,j.isValidElement)(t)&&!D(t.type)?(0,j.cloneElement)(t,{closeToast:o.closeToast,toastProps:o,data:o.data,isPaused:e}):P(t)?t({closeToast:o.closeToast,toastProps:o,data:o.data,isPaused:e}):t}var Z=w(require("react"));function It({closeToast:t,theme:o,ariaLabel:e="close"}){return Z.default.createElement("button",{className:`Toastify__close-button Toastify__close-button--${o}`,type:"button",onClick:n=>{n.stopPropagation(),t(!0)},"aria-label":e},Z.default.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},Z.default.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}var J=w(require("react")),pt=w(require("clsx"));function St({delay:t,isRunning:o,closeToast:e,type:n="default",hide:a,className:l,controlledProgress:s,progress:d,rtl:c,isIn:T,theme:g}){let v=a||s&&d===0,x={animationDuration:`${t}ms`,animationPlayState:o?"running":"paused"};s&&(x.transform=`scaleX(${d})`);let C=(0,pt.default)("Toastify__progress-bar",s?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${g}`,`Toastify__progress-bar--${n}`,{["Toastify__progress-bar--rtl"]:c}),k=P(l)?l({rtl:c,type:n,defaultClassName:C}):(0,pt.default)(C,l),E={[s&&d>=1?"onTransitionEnd":"onAnimationEnd"]:s&&d<1?null:()=>{T&&e()}};return J.default.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":v},J.default.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${g} Toastify__progress-bar--${n}`}),J.default.createElement("div",{role:"progressbar","aria-hidden":v?"true":"false","aria-label":"notification timer",className:k,style:x,...E}))}var _t=w(require("clsx")),N=w(require("react"));var Jt=1,ut=()=>`${Jt++}`;function kt(t,o,e){let n=1,a=0,l=[],s=[],d=o,c=new Map,T=new Set,g=i=>(T.add(i),()=>T.delete(i)),v=()=>{s=Array.from(c.values()),T.forEach(i=>i())},x=({containerId:i,toastId:r,updateId:u})=>{let h=i?i!==t:t!==1,m=c.has(r)&&u==null;return h||m},C=(i,r)=>{c.forEach(u=>{var h;(r==null||r===u.props.toastId)&&((h=u.toggle)==null||h.call(u,i))})},k=i=>{var r,u;(u=(r=i.props)==null?void 0:r.onClose)==null||u.call(r,i.removalReason),i.isActive=!1},E=i=>{if(i==null)c.forEach(k);else{let r=c.get(i);r&&k(r)}v()},f=()=>{a-=l.length,l=[]},p=i=>{var m,_;let{toastId:r,updateId:u}=i.props,h=u==null;i.staleId&&c.delete(i.staleId),i.isActive=!0,c.set(r,i),v(),e(dt(i,h?"added":"updated")),h&&((_=(m=i.props).onOpen)==null||_.call(m))};return{id:t,props:d,observe:g,toggle:C,removeToast:E,toasts:c,clearQueue:f,buildToast:(i,r)=>{if(x(r))return;let{toastId:u,updateId:h,data:m,staleId:_,delay:A}=r,U=h==null;U&&a++;let O={...d,style:d.toastStyle,key:n++,...Object.fromEntries(Object.entries(r).filter(([R,ct])=>ct!=null)),toastId:u,updateId:h,data:m,isIn:!1,className:X(r.className||d.toastClassName),progressClassName:X(r.progressClassName||d.progressClassName),autoClose:r.isLoading?!1:Pt(r.autoClose,d.autoClose),closeToast(R){c.get(u).removalReason=R,E(u)},deleteToast(){let R=c.get(u);if(R!=null){if(e(dt(R,"removed")),c.delete(u),a--,a<0&&(a=0),l.length>0){p(l.shift());return}v()}}};O.closeButton=d.closeButton,r.closeButton===!1||V(r.closeButton)?O.closeButton=r.closeButton:r.closeButton===!0&&(O.closeButton=V(d.closeButton)?d.closeButton:!0);let H={content:i,props:O,staleId:_};d.limit&&d.limit>0&&a>d.limit&&U?l.push(H):z(A)?setTimeout(()=>{p(H)},A):p(H)},setProps(i){d=i},setToggle:(i,r)=>{let u=c.get(i);u&&(u.toggle=r)},isToastActive:i=>{var r;return(r=c.get(i))==null?void 0:r.isActive},getSnapshot:()=>s}}var I=new Map,Q=[],yt=new Set,to=t=>yt.forEach(o=>o(t)),At=()=>I.size>0;function oo(){Q.forEach(t=>Tt(t.content,t.options)),Q=[]}var Ot=(t,{containerId:o})=>{var e;return(e=I.get(o||1))==null?void 0:e.toasts.get(t)};function tt(t,o){var n;if(o)return!!((n=I.get(o))!=null&&n.isToastActive(t));let e=!1;return I.forEach(a=>{a.isToastActive(t)&&(e=!0)}),e}function Nt(t){if(!At()){Q=Q.filter(o=>t!=null&&o.options.toastId!==t);return}if(t==null||Et(t))I.forEach(o=>{o.removeToast(t)});else if(t&&("containerId"in t||"id"in t)){let o=I.get(t.containerId);o?o.removeToast(t.id):I.forEach(e=>{e.removeToast(t.id)})}}var wt=(t={})=>{I.forEach(o=>{o.props.limit&&(!t.containerId||o.id===t.containerId)&&o.clearQueue()})};function Tt(t,o){V(t)&&(At()||Q.push({content:t,options:o}),I.forEach(e=>{e.buildToast(t,o)}))}function Dt(t){var o;(o=I.get(t.containerId||1))==null||o.setToggle(t.id,t.fn)}function gt(t,o){I.forEach(e=>{(o==null||!(o!=null&&o.containerId)||(o==null?void 0:o.containerId)===e.id)&&e.toggle(t,o==null?void 0:o.id)})}function Lt(t){let o=t.containerId||1;return{subscribe(e){let n=kt(o,t,to);I.set(o,n);let a=n.observe(e);return oo(),()=>{a(),I.delete(o)}},setProps(e){var n;(n=I.get(o))==null||n.setProps(e)},getSnapshot(){var e;return(e=I.get(o))==null?void 0:e.getSnapshot()}}}function Mt(t){return yt.add(t),()=>{yt.delete(t)}}function eo(t){return t&&(D(t.toastId)||z(t.toastId))?t.toastId:ut()}function W(t,o){return Tt(t,o),o.toastId}function ot(t,o){return{...o,type:o&&o.type||t,toastId:eo(o)}}function et(t){return(o,e)=>W(o,ot(t,e))}function y(t,o){return W(t,ot("default",o))}y.loading=(t,o)=>W(t,ot("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...o}));function ao(t,{pending:o,error:e,success:n},a){let l;o&&(l=D(o)?y.loading(o,a):y.loading(o.render,{...a,...o}));let s={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(T,g,v)=>{if(g==null){y.dismiss(l);return}let x={type:T,...s,...a,data:v},C=D(g)?{render:g}:g;return l?y.update(l,{...x,...C}):y(C.render,{...x,...C}),v},c=P(t)?t():t;return c.then(T=>d("success",n,T)).catch(T=>d("error",e,T)),c}y.promise=ao;y.success=et("success");y.info=et("info");y.error=et("error");y.warning=et("warning");y.warn=y.warning;y.dark=(t,o)=>W(t,ot("default",{theme:"dark",...o}));function so(t){Nt(t)}y.dismiss=so;y.clearWaitingQueue=wt;y.isActive=tt;y.update=(t,o={})=>{let e=Ot(t,o);if(e){let{props:n,content:a}=e,l={delay:100,...n,...o,toastId:o.toastId||t,updateId:ut()};l.toastId!==t&&(l.staleId=t);let s=l.render||a;delete l.render,W(s,l)}};y.done=t=>{y.update(t,{progress:1})};y.onChange=Mt;y.play=t=>gt(!0,t);y.pause=t=>gt(!1,t);var at=require("react");function $t(t){var s;let{subscribe:o,getSnapshot:e,setProps:n}=(0,at.useRef)(Lt(t)).current;n(t);let a=(s=(0,at.useSyncExternalStore)(o,e,e))==null?void 0:s.slice();function l(d){if(!a)return[];let c=new Map;return t.newestOnTop&&a.reverse(),a.forEach(T=>{let{position:g}=T.props;c.has(g)||c.set(g,[]),c.get(g).push(T)}),Array.from(c,T=>d(T[0],T[1]))}return{getToastToRender:l,isToastActive:tt,count:a==null?void 0:a.length}}var B=require("react");function Rt(t){let[o,e]=(0,B.useState)(!1),[n,a]=(0,B.useState)(!1),l=(0,B.useRef)(null),s=(0,B.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:c,closeToast:T,onClick:g,closeOnClick:v}=t;Dt({id:t.toastId,containerId:t.containerId,fn:e}),(0,B.useEffect)(()=>{if(t.pauseOnFocusLoss)return x(),()=>{C()}},[t.pauseOnFocusLoss]);function x(){document.hasFocus()||p(),window.addEventListener("focus",f),window.addEventListener("blur",p)}function C(){window.removeEventListener("focus",f),window.removeEventListener("blur",p)}function k(m){if(t.draggable===!0||t.draggable===m.pointerType){b();let _=l.current;s.canCloseOnClick=!0,s.canDrag=!0,_.style.transition="none",t.draggableDirection==="x"?(s.start=m.clientX,s.removalDistance=_.offsetWidth*(t.draggablePercent/100)):(s.start=m.clientY,s.removalDistance=_.offsetHeight*(t.draggablePercent===80?t.draggablePercent*1.5:t.draggablePercent)/100)}}function E(m){let{top:_,bottom:A,left:U,right:O}=l.current.getBoundingClientRect();m.nativeEvent.type!=="touchend"&&t.pauseOnHover&&m.clientX>=U&&m.clientX<=O&&m.clientY>=_&&m.clientY<=A?p():f()}function f(){e(!0)}function p(){e(!1)}function b(){s.didMove=!1,document.addEventListener("pointermove",r),document.addEventListener("pointerup",u)}function i(){document.removeEventListener("pointermove",r),document.removeEventListener("pointerup",u)}function r(m){let _=l.current;if(s.canDrag&&_){s.didMove=!0,o&&p(),t.draggableDirection==="x"?s.delta=m.clientX-s.start:s.delta=m.clientY-s.start,s.start!==m.clientX&&(s.canCloseOnClick=!1);let A=t.draggableDirection==="x"?`${s.delta}px, var(--y)`:`0, calc(${s.delta}px + var(--y))`;_.style.transform=`translate3d(${A},0)`,_.style.opacity=`${1-Math.abs(s.delta/s.removalDistance)}`}}function u(){i();let m=l.current;if(s.canDrag&&s.didMove&&m){if(s.canDrag=!1,Math.abs(s.delta)>s.removalDistance){a(!0),t.closeToast(!0),t.collapseAll();return}m.style.transition="transform 0.2s, opacity 0.2s",m.style.removeProperty("transform"),m.style.removeProperty("opacity")}}let h={onPointerDown:k,onPointerUp:E};return d&&c&&(h.onMouseEnter=p,t.stacked||(h.onMouseLeave=f)),v&&(h.onClick=m=>{g&&g(m),s.canCloseOnClick&&T(!0)}),{playToast:f,pauseToast:p,isRunning:o,preventExitTransition:n,toastRef:l,eventHandlers:h}}var st=require("react"),Bt=typeof window!="undefined"?st.useLayoutEffect:st.useEffect;var rt=w(require("clsx")),$=w(require("react"));var S=w(require("react"));var nt=({theme:t,type:o,isLoading:e,...n})=>S.default.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:t==="colored"?"currentColor":`var(--toastify-icon-color-${o})`,...n});function ro(t){return S.default.createElement(nt,{...t},S.default.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))}function io(t){return S.default.createElement(nt,{...t},S.default.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))}function lo(t){return S.default.createElement(nt,{...t},S.default.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))}function co(t){return S.default.createElement(nt,{...t},S.default.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))}function fo(){return S.default.createElement("div",{className:"Toastify__spinner"})}var G={info:io,warning:ro,success:lo,error:co,spinner:fo},mo=t=>t in G;function zt({theme:t,type:o,isLoading:e,icon:n}){let a=null,l={theme:t,type:o};return n===!1||(P(n)?a=n({...l,isLoading:e}):(0,S.isValidElement)(n)?a=(0,S.cloneElement)(n,l):e?a=G.spinner():mo(o)&&(a=G[o](l))),a}var Ft=t=>{let{isRunning:o,preventExitTransition:e,toastRef:n,eventHandlers:a,playToast:l}=Rt(t),{closeButton:s,children:d,autoClose:c,onClick:T,type:g,hideProgressBar:v,closeToast:x,transition:C,position:k,className:E,style:f,progressClassName:p,updateId:b,role:i,progress:r,rtl:u,toastId:h,deleteToast:m,isIn:_,isLoading:A,closeOnClick:U,theme:O,ariaLabel:H}=t,R=(0,rt.default)("Toastify__toast",`Toastify__toast-theme--${O}`,`Toastify__toast--${g}`,{["Toastify__toast--rtl"]:u},{["Toastify__toast--close-on-click"]:U}),ct=P(E)?E({rtl:u,position:k,type:g,defaultClassName:R}):(0,rt.default)(R,E),vt=zt(t),ht=!!r||!c,ft={closeToast:x,type:g,theme:O},q=null;return s===!1||(P(s)?q=s(ft):(0,$.isValidElement)(s)?q=(0,$.cloneElement)(s,ft):q=It(ft)),$.default.createElement(C,{isIn:_,done:m,position:k,preventExitTransition:e,nodeRef:n,playToast:l},$.default.createElement("div",{id:h,tabIndex:0,onClick:T,"data-in":_,className:ct,...a,style:f,ref:n,..._&&{role:i,"aria-label":H}},vt!=null&&$.default.createElement("div",{className:(0,rt.default)("Toastify__toast-icon",{["Toastify--animate-icon Toastify__zoom-enter"]:!A})},vt),mt(d,t,!o),q,!t.customProgressBar&&$.default.createElement(St,{...b&&!ht?{key:`p-${b}`}:{},rtl:u,theme:O,delay:c,isRunning:o,isIn:_,closeToast:x,hide:v,type:g,className:p,controlledProgress:ht,progress:r||0})))};var it=(t,o=!1)=>({enter:`Toastify--animate Toastify__${t}-enter`,exit:`Toastify--animate Toastify__${t}-exit`,appendPosition:o}),lt=F(it("bounce",!0)),Ut=F(it("slide",!0)),Ht=F(it("zoom")),Xt=F(it("flip"));var po={position:"top-right",transition:lt,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light","aria-label":"Notifications Alt+T",hotKeys:t=>t.altKey&&t.code==="KeyT"};function bt(t){let o={...po,...t},e=t.stacked,[n,a]=(0,N.useState)(!0),l=(0,N.useRef)(null),{getToastToRender:s,isToastActive:d,count:c}=$t(o),{className:T,style:g,rtl:v,containerId:x,hotKeys:C}=o;function k(f){let p=(0,_t.default)("Toastify__toast-container",`Toastify__toast-container--${f}`,{["Toastify__toast-container--rtl"]:v});return P(T)?T({position:f,rtl:v,defaultClassName:p}):(0,_t.default)(p,X(T))}function E(){e&&(a(!0),y.play())}return Bt(()=>{var f;if(e){let p=l.current.querySelectorAll('[data-in="true"]'),b=12,i=(f=o.position)==null?void 0:f.includes("top"),r=0,u=0;Array.from(p).reverse().forEach((h,m)=>{let _=h;_.classList.add("Toastify__toast--stacked"),m>0&&(_.dataset.collapsed=`${n}`),_.dataset.pos||(_.dataset.pos=i?"top":"bot");let A=r*(n?.2:1)+(n?0:b*m);_.style.setProperty("--y",`${i?A:A*-1}px`),_.style.setProperty("--g",`${b}`),_.style.setProperty("--s",`${1-(n?u:0)}`),r+=_.offsetHeight,u+=.025})}},[n,c,e]),(0,N.useEffect)(()=>{function f(p){var i;let b=l.current;C(p)&&((i=b.querySelector('[tabIndex="0"]'))==null||i.focus(),a(!1),y.pause()),p.key==="Escape"&&(document.activeElement===b||b!=null&&b.contains(document.activeElement))&&(a(!0),y.play())}return document.addEventListener("keydown",f),()=>{document.removeEventListener("keydown",f)}},[C]),N.default.createElement("section",{ref:l,className:"Toastify",id:x,onMouseEnter:()=>{e&&(a(!1),y.pause())},onMouseLeave:E,"aria-live":"polite","aria-atomic":"false","aria-relevant":"additions text","aria-label":o["aria-label"]},s((f,p)=>{let b=p.length?{...g}:{...g,pointerEvents:"none"};return N.default.createElement("div",{tabIndex:-1,className:k(f),"data-stacked":e,style:b,key:`c-${f}`},p.map(({content:i,props:r})=>N.default.createElement(Ft,{...r,stacked:e,collapseAll:E,isIn:d(r.toastId,r.containerId),key:`t-${r.key}`},i)))}))}0&&(module.exports={Bounce,Flip,Icons,Slide,ToastContainer,Zoom,collapseToast,cssTransition,toast});
//# sourceMappingURL=index.js.map