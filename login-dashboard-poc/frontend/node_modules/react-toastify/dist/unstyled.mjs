"use client";
import{isValidElement as Mt}from"react";var L=t=>typeof t=="number"&&!isNaN(t),N=t=>typeof t=="string",P=t=>typeof t=="function",mt=t=>N(t)||L(t),B=t=>N(t)||P(t)?t:null,pt=(t,o)=>t===!1||L(t)&&t>0?t:o,z=t=>Mt(t)||N(t)||P(t)||L(t);import ut,{useEffect as $t,useLayoutEffect as Rt,useRef as Bt}from"react";function Z(t,o,e=300){let{scrollHeight:n,style:s}=t;requestAnimationFrame(()=>{s.minHeight="initial",s.height=n+"px",s.transition=`all ${e}ms`,requestAnimationFrame(()=>{s.height="0",s.padding="0",s.margin="0",setTimeout(o,e)})})}function $({enter:t,exit:o,appendPosition:e=!1,collapse:n=!0,collapseDuration:s=300}){return function({children:a,position:d,preventExitTransition:f,done:T,nodeRef:g,isIn:v,playToast:x}){let C=e?`${t}--${d}`:t,S=e?`${o}--${d}`:o,E=Bt(0);return Rt(()=>{let c=g.current,p=C.split(" "),b=r=>{r.target===g.current&&(x(),c.removeEventListener("animationend",b),c.removeEventListener("animationcancel",b),E.current===0&&r.type!=="animationcancel"&&c.classList.remove(...p))};(()=>{c.classList.add(...p),c.addEventListener("animationend",b),c.addEventListener("animationcancel",b)})()},[]),$t(()=>{let c=g.current,p=()=>{c.removeEventListener("animationend",p),n?Z(c,T,s):T()};v||(f?p():(()=>{E.current=1,c.className+=` ${S}`,c.addEventListener("animationend",p)})())},[v]),ut.createElement(ut.Fragment,null,a)}}import{cloneElement as zt,isValidElement as Ft}from"react";function J(t,o){return{content:tt(t.content,t.props),containerId:t.props.containerId,id:t.props.toastId,theme:t.props.theme,type:t.props.type,data:t.props.data||{},isLoading:t.props.isLoading,icon:t.props.icon,reason:t.removalReason,status:o}}function tt(t,o,e=!1){return Ft(t)&&!N(t.type)?zt(t,{closeToast:o.closeToast,toastProps:o,data:o.data,isPaused:e}):P(t)?t({closeToast:o.closeToast,toastProps:o,data:o.data,isPaused:e}):t}import ot from"react";function yt({closeToast:t,theme:o,ariaLabel:e="close"}){return ot.createElement("button",{className:`Toastify__close-button Toastify__close-button--${o}`,type:"button",onClick:n=>{n.stopPropagation(),t(!0)},"aria-label":e},ot.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},ot.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}import et from"react";import Tt from"clsx";function gt({delay:t,isRunning:o,closeToast:e,type:n="default",hide:s,className:l,controlledProgress:a,progress:d,rtl:f,isIn:T,theme:g}){let v=s||a&&d===0,x={animationDuration:`${t}ms`,animationPlayState:o?"running":"paused"};a&&(x.transform=`scaleX(${d})`);let C=Tt("Toastify__progress-bar",a?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${g}`,`Toastify__progress-bar--${n}`,{["Toastify__progress-bar--rtl"]:f}),S=P(l)?l({rtl:f,type:n,defaultClassName:C}):Tt(C,l),E={[a&&d>=1?"onTransitionEnd":"onAnimationEnd"]:a&&d<1?null:()=>{T&&e()}};return et.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":v},et.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${g} Toastify__progress-bar--${n}`}),et.createElement("div",{role:"progressbar","aria-hidden":v?"true":"false","aria-label":"notification timer",className:S,style:x,...E}))}import Dt from"clsx";import ft,{useEffect as uo,useRef as yo,useState as To}from"react";var Ht=1,at=()=>`${Ht++}`;function _t(t,o,e){let n=1,s=0,l=[],a=[],d=o,f=new Map,T=new Set,g=i=>(T.add(i),()=>T.delete(i)),v=()=>{a=Array.from(f.values()),T.forEach(i=>i())},x=({containerId:i,toastId:r,updateId:u})=>{let h=i?i!==t:t!==1,m=f.has(r)&&u==null;return h||m},C=(i,r)=>{f.forEach(u=>{var h;(r==null||r===u.props.toastId)&&((h=u.toggle)==null||h.call(u,i))})},S=i=>{var r,u;(u=(r=i.props)==null?void 0:r.onClose)==null||u.call(r,i.removalReason),i.isActive=!1},E=i=>{if(i==null)f.forEach(S);else{let r=f.get(i);r&&S(r)}v()},c=()=>{s-=l.length,l=[]},p=i=>{var m,_;let{toastId:r,updateId:u}=i.props,h=u==null;i.staleId&&f.delete(i.staleId),i.isActive=!0,f.set(r,i),v(),e(J(i,h?"added":"updated")),h&&((_=(m=i.props).onOpen)==null||_.call(m))};return{id:t,props:d,observe:g,toggle:C,removeToast:E,toasts:f,clearQueue:c,buildToast:(i,r)=>{if(x(r))return;let{toastId:u,updateId:h,data:m,staleId:_,delay:k}=r,M=h==null;M&&s++;let A={...d,style:d.toastStyle,key:n++,...Object.fromEntries(Object.entries(r).filter(([D,Y])=>Y!=null)),toastId:u,updateId:h,data:m,isIn:!1,className:B(r.className||d.toastClassName),progressClassName:B(r.progressClassName||d.progressClassName),autoClose:r.isLoading?!1:pt(r.autoClose,d.autoClose),closeToast(D){f.get(u).removalReason=D,E(u)},deleteToast(){let D=f.get(u);if(D!=null){if(e(J(D,"removed")),f.delete(u),s--,s<0&&(s=0),l.length>0){p(l.shift());return}v()}}};A.closeButton=d.closeButton,r.closeButton===!1||z(r.closeButton)?A.closeButton=r.closeButton:r.closeButton===!0&&(A.closeButton=z(d.closeButton)?d.closeButton:!0);let R={content:i,props:A,staleId:_};d.limit&&d.limit>0&&s>d.limit&&M?l.push(R):L(k)?setTimeout(()=>{p(R)},k):p(R)},setProps(i){d=i},setToggle:(i,r)=>{let u=f.get(i);u&&(u.toggle=r)},isToastActive:i=>{var r;return(r=f.get(i))==null?void 0:r.isActive},getSnapshot:()=>a}}var I=new Map,F=[],st=new Set,Vt=t=>st.forEach(o=>o(t)),bt=()=>I.size>0;function Xt(){F.forEach(t=>rt(t.content,t.options)),F=[]}var vt=(t,{containerId:o})=>{var e;return(e=I.get(o||1))==null?void 0:e.toasts.get(t)};function V(t,o){var n;if(o)return!!((n=I.get(o))!=null&&n.isToastActive(t));let e=!1;return I.forEach(s=>{s.isToastActive(t)&&(e=!0)}),e}function ht(t){if(!bt()){F=F.filter(o=>t!=null&&o.options.toastId!==t);return}if(t==null||mt(t))I.forEach(o=>{o.removeToast(t)});else if(t&&("containerId"in t||"id"in t)){let o=I.get(t.containerId);o?o.removeToast(t.id):I.forEach(e=>{e.removeToast(t.id)})}}var Ct=(t={})=>{I.forEach(o=>{o.props.limit&&(!t.containerId||o.id===t.containerId)&&o.clearQueue()})};function rt(t,o){z(t)&&(bt()||F.push({content:t,options:o}),I.forEach(e=>{e.buildToast(t,o)}))}function xt(t){var o;(o=I.get(t.containerId||1))==null||o.setToggle(t.id,t.fn)}function nt(t,o){I.forEach(e=>{(o==null||!(o!=null&&o.containerId)||(o==null?void 0:o.containerId)===e.id)&&e.toggle(t,o==null?void 0:o.id)})}function Et(t){let o=t.containerId||1;return{subscribe(e){let n=_t(o,t,Vt);I.set(o,n);let s=n.observe(e);return Xt(),()=>{s(),I.delete(o)}},setProps(e){var n;(n=I.get(o))==null||n.setProps(e)},getSnapshot(){var e;return(e=I.get(o))==null?void 0:e.getSnapshot()}}}function Pt(t){return st.add(t),()=>{st.delete(t)}}function Qt(t){return t&&(N(t.toastId)||L(t.toastId))?t.toastId:at()}function U(t,o){return rt(t,o),o.toastId}function X(t,o){return{...o,type:o&&o.type||t,toastId:Qt(o)}}function Q(t){return(o,e)=>U(o,X(t,e))}function y(t,o){return U(t,X("default",o))}y.loading=(t,o)=>U(t,X("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...o}));function Wt(t,{pending:o,error:e,success:n},s){let l;o&&(l=N(o)?y.loading(o,s):y.loading(o.render,{...s,...o}));let a={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(T,g,v)=>{if(g==null){y.dismiss(l);return}let x={type:T,...a,...s,data:v},C=N(g)?{render:g}:g;return l?y.update(l,{...x,...C}):y(C.render,{...x,...C}),v},f=P(t)?t():t;return f.then(T=>d("success",n,T)).catch(T=>d("error",e,T)),f}y.promise=Wt;y.success=Q("success");y.info=Q("info");y.error=Q("error");y.warning=Q("warning");y.warn=y.warning;y.dark=(t,o)=>U(t,X("default",{theme:"dark",...o}));function Gt(t){ht(t)}y.dismiss=Gt;y.clearWaitingQueue=Ct;y.isActive=V;y.update=(t,o={})=>{let e=vt(t,o);if(e){let{props:n,content:s}=e,l={delay:100,...n,...o,toastId:o.toastId||t,updateId:at()};l.toastId!==t&&(l.staleId=t);let a=l.render||s;delete l.render,U(a,l)}};y.done=t=>{y.update(t,{progress:1})};y.onChange=Pt;y.play=t=>nt(!0,t);y.pause=t=>nt(!1,t);import{useRef as qt,useSyncExternalStore as Kt}from"react";function It(t){var a;let{subscribe:o,getSnapshot:e,setProps:n}=qt(Et(t)).current;n(t);let s=(a=Kt(o,e,e))==null?void 0:a.slice();function l(d){if(!s)return[];let f=new Map;return t.newestOnTop&&s.reverse(),s.forEach(T=>{let{position:g}=T.props;f.has(g)||f.set(g,[]),f.get(g).push(T)}),Array.from(f,T=>d(T[0],T[1]))}return{getToastToRender:l,isToastActive:V,count:s==null?void 0:s.length}}import{useEffect as jt,useRef as St,useState as kt}from"react";function At(t){let[o,e]=kt(!1),[n,s]=kt(!1),l=St(null),a=St({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:f,closeToast:T,onClick:g,closeOnClick:v}=t;xt({id:t.toastId,containerId:t.containerId,fn:e}),jt(()=>{if(t.pauseOnFocusLoss)return x(),()=>{C()}},[t.pauseOnFocusLoss]);function x(){document.hasFocus()||p(),window.addEventListener("focus",c),window.addEventListener("blur",p)}function C(){window.removeEventListener("focus",c),window.removeEventListener("blur",p)}function S(m){if(t.draggable===!0||t.draggable===m.pointerType){b();let _=l.current;a.canCloseOnClick=!0,a.canDrag=!0,_.style.transition="none",t.draggableDirection==="x"?(a.start=m.clientX,a.removalDistance=_.offsetWidth*(t.draggablePercent/100)):(a.start=m.clientY,a.removalDistance=_.offsetHeight*(t.draggablePercent===80?t.draggablePercent*1.5:t.draggablePercent)/100)}}function E(m){let{top:_,bottom:k,left:M,right:A}=l.current.getBoundingClientRect();m.nativeEvent.type!=="touchend"&&t.pauseOnHover&&m.clientX>=M&&m.clientX<=A&&m.clientY>=_&&m.clientY<=k?p():c()}function c(){e(!0)}function p(){e(!1)}function b(){a.didMove=!1,document.addEventListener("pointermove",r),document.addEventListener("pointerup",u)}function i(){document.removeEventListener("pointermove",r),document.removeEventListener("pointerup",u)}function r(m){let _=l.current;if(a.canDrag&&_){a.didMove=!0,o&&p(),t.draggableDirection==="x"?a.delta=m.clientX-a.start:a.delta=m.clientY-a.start,a.start!==m.clientX&&(a.canCloseOnClick=!1);let k=t.draggableDirection==="x"?`${a.delta}px, var(--y)`:`0, calc(${a.delta}px + var(--y))`;_.style.transform=`translate3d(${k},0)`,_.style.opacity=`${1-Math.abs(a.delta/a.removalDistance)}`}}function u(){i();let m=l.current;if(a.canDrag&&a.didMove&&m){if(a.canDrag=!1,Math.abs(a.delta)>a.removalDistance){s(!0),t.closeToast(!0),t.collapseAll();return}m.style.transition="transform 0.2s, opacity 0.2s",m.style.removeProperty("transform"),m.style.removeProperty("opacity")}}let h={onPointerDown:S,onPointerUp:E};return d&&f&&(h.onMouseEnter=p,t.stacked||(h.onMouseLeave=c)),v&&(h.onClick=m=>{g&&g(m),a.canCloseOnClick&&T(!0)}),{playToast:c,pauseToast:p,isRunning:o,preventExitTransition:n,toastRef:l,eventHandlers:h}}import{useEffect as Zt,useLayoutEffect as Jt}from"react";var Ot=typeof window!="undefined"?Jt:Zt;import it from"clsx";import q,{cloneElement as lo,isValidElement as fo}from"react";import O,{cloneElement as to,isValidElement as oo}from"react";var G=({theme:t,type:o,isLoading:e,...n})=>O.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:t==="colored"?"currentColor":`var(--toastify-icon-color-${o})`,...n});function eo(t){return O.createElement(G,{...t},O.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))}function ao(t){return O.createElement(G,{...t},O.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))}function so(t){return O.createElement(G,{...t},O.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))}function ro(t){return O.createElement(G,{...t},O.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))}function no(){return O.createElement("div",{className:"Toastify__spinner"})}var W={info:ao,warning:eo,success:so,error:ro,spinner:no},io=t=>t in W;function Nt({theme:t,type:o,isLoading:e,icon:n}){let s=null,l={theme:t,type:o};return n===!1||(P(n)?s=n({...l,isLoading:e}):oo(n)?s=to(n,l):e?s=W.spinner():io(o)&&(s=W[o](l))),s}var wt=t=>{let{isRunning:o,preventExitTransition:e,toastRef:n,eventHandlers:s,playToast:l}=At(t),{closeButton:a,children:d,autoClose:f,onClick:T,type:g,hideProgressBar:v,closeToast:x,transition:C,position:S,className:E,style:c,progressClassName:p,updateId:b,role:i,progress:r,rtl:u,toastId:h,deleteToast:m,isIn:_,isLoading:k,closeOnClick:M,theme:A,ariaLabel:R}=t,D=it("Toastify__toast",`Toastify__toast-theme--${A}`,`Toastify__toast--${g}`,{["Toastify__toast--rtl"]:u},{["Toastify__toast--close-on-click"]:M}),Y=P(E)?E({rtl:u,position:S,type:g,defaultClassName:D}):it(D,E),ct=Nt(t),dt=!!r||!f,j={closeToast:x,type:g,theme:A},H=null;return a===!1||(P(a)?H=a(j):fo(a)?H=lo(a,j):H=yt(j)),q.createElement(C,{isIn:_,done:m,position:S,preventExitTransition:e,nodeRef:n,playToast:l},q.createElement("div",{id:h,tabIndex:0,onClick:T,"data-in":_,className:Y,...s,style:c,ref:n,..._&&{role:i,"aria-label":R}},ct!=null&&q.createElement("div",{className:it("Toastify__toast-icon",{["Toastify--animate-icon Toastify__zoom-enter"]:!k})},ct),tt(d,t,!o),H,!t.customProgressBar&&q.createElement(gt,{...b&&!dt?{key:`p-${b}`}:{},rtl:u,theme:A,delay:f,isRunning:o,isIn:_,closeToast:x,hide:v,type:g,className:p,controlledProgress:dt,progress:r||0})))};var K=(t,o=!1)=>({enter:`Toastify--animate Toastify__${t}-enter`,exit:`Toastify--animate Toastify__${t}-exit`,appendPosition:o}),lt=$(K("bounce",!0)),co=$(K("slide",!0)),mo=$(K("zoom")),po=$(K("flip"));var go={position:"top-right",transition:lt,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light","aria-label":"Notifications Alt+T",hotKeys:t=>t.altKey&&t.code==="KeyT"};function Lt(t){let o={...go,...t},e=t.stacked,[n,s]=To(!0),l=yo(null),{getToastToRender:a,isToastActive:d,count:f}=It(o),{className:T,style:g,rtl:v,containerId:x,hotKeys:C}=o;function S(c){let p=Dt("Toastify__toast-container",`Toastify__toast-container--${c}`,{["Toastify__toast-container--rtl"]:v});return P(T)?T({position:c,rtl:v,defaultClassName:p}):Dt(p,B(T))}function E(){e&&(s(!0),y.play())}return Ot(()=>{var c;if(e){let p=l.current.querySelectorAll('[data-in="true"]'),b=12,i=(c=o.position)==null?void 0:c.includes("top"),r=0,u=0;Array.from(p).reverse().forEach((h,m)=>{let _=h;_.classList.add("Toastify__toast--stacked"),m>0&&(_.dataset.collapsed=`${n}`),_.dataset.pos||(_.dataset.pos=i?"top":"bot");let k=r*(n?.2:1)+(n?0:b*m);_.style.setProperty("--y",`${i?k:k*-1}px`),_.style.setProperty("--g",`${b}`),_.style.setProperty("--s",`${1-(n?u:0)}`),r+=_.offsetHeight,u+=.025})}},[n,f,e]),uo(()=>{function c(p){var i;let b=l.current;C(p)&&((i=b.querySelector('[tabIndex="0"]'))==null||i.focus(),s(!1),y.pause()),p.key==="Escape"&&(document.activeElement===b||b!=null&&b.contains(document.activeElement))&&(s(!0),y.play())}return document.addEventListener("keydown",c),()=>{document.removeEventListener("keydown",c)}},[C]),ft.createElement("section",{ref:l,className:"Toastify",id:x,onMouseEnter:()=>{e&&(s(!1),y.pause())},onMouseLeave:E,"aria-live":"polite","aria-atomic":"false","aria-relevant":"additions text","aria-label":o["aria-label"]},a((c,p)=>{let b=p.length?{...g}:{...g,pointerEvents:"none"};return ft.createElement("div",{tabIndex:-1,className:S(c),"data-stacked":e,style:b,key:`c-${c}`},p.map(({content:i,props:r})=>ft.createElement(wt,{...r,stacked:e,collapseAll:E,isIn:d(r.toastId,r.containerId),key:`t-${r.key}`},i)))}))}export{lt as Bounce,po as Flip,W as Icons,co as Slide,Lt as ToastContainer,mo as Zoom,Z as collapseToast,$ as cssTransition,y as toast};
//# sourceMappingURL=unstyled.mjs.map