{"version": "11.0.5", "license": "MIT", "description": "React notification made easy", "keywords": ["react", "notification", "toast", "react-component", "react-toastify", "push", "alert", "snackbar", "message"], "files": ["dist", "addons"], "scripts": {"prepare": "lefthook install", "setup": "pnpm link .", "start": "cd playground && pnpm dev", "test": "cypress open --component", "test:run": "cypress run --component -b chrome", "prettier": "prettier --write src", "build": "tsup && cp src/style.css dist/ReactToastify.css && rm dist/unstyled.css*"}, "peerDependencies": {"react": "^18 || ^19", "react-dom": "^18 || ^19"}, "prettier": {"printWidth": 120, "semi": true, "singleQuote": true, "trailingComma": "none", "arrowParens": "avoid"}, "name": "react-toastify", "repository": {"type": "git", "url": "git+https://github.com/fkhadra/react-toastify.git"}, "author": "<PERSON><PERSON> <<EMAIL>> (https://fkhadra.github.io)", "bugs": {"url": "https://github.com/fkhadra/react-toastify/issues"}, "homepage": "https://github.com/fkhadra/react-toastify#readme", "devDependencies": {"@4tw/cypress-drag-drop": "^2.2.5", "@cypress/code-coverage": "^3.13.9", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@testing-library/cypress": "^10.0.2", "@types/node": "^22.10.2", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.3.4", "coveralls": "^3.1.1", "cypress": "^13.16.1", "lefthook": "^1.9.2", "lint-staged": "^15.2.11", "postcss": "^8.4.49", "prettier": "3.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "tsup": "^8.3.5", "typescript": "^5.7.2", "vite": "^6.0.3", "vite-plugin-istanbul": "^6.0.2"}, "dependencies": {"clsx": "^2.1.1"}, "main": "dist/index.js", "typings": "dist/index.d.ts", "module": "dist/index.mjs", "source": "src/index.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./unstyled": {"types": "./dist/unstyled.d.ts", "import": "./dist/unstyled.mjs", "require": "./dist/unstyled.js"}, "./dist/ReactToastify.css": "./dist/ReactToastify.css", "./ReactToastify.css": "./dist/ReactToastify.css", "./package.json": "./package.json", "./addons/use-notification-center": {"types": "./addons/use-notification-center/index.d.ts", "import": "./addons/use-notification-center/index.mjs", "require": "./addons/use-notification-center/index.js"}, "./notification-center": {"types": "./addons/use-notification-center/index.d.ts", "import": "./addons/use-notification-center/index.mjs", "require": "./addons/use-notification-center/index.js"}}, "lint-staged": {"*.{js,jsx,ts,tsx,md,html,css}": "prettier --write"}}