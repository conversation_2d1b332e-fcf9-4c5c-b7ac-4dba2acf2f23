const path = require('path');
const resolve = require('resolve');

describe('dotdot', () => {
  it('should resolve ..', () => {
    expect(resolve.sync('a/b/c', { basedir: 'a/b', paths: ['c'] })).toBe(path.join('a', 'c'));
    expect(resolve.sync('a/b/c/../d', { basedir: 'a/b', paths: ['c'] })).toBe(path.join('a', 'b', 'd'));
    expect(resolve.sync('a/b/c/../d', { basedir: 'a/b', paths: ['c'], extensions: ['.js'] })).toBe(path.join('a', 'b', 'd'));
  });

  it('should not resolve .. to parent directory', () => {
    expect(resolve.sync('../c', { basedir: 'a/b', paths: ['c'], allowZeroBasedPath: true })).toBe(path.join('a', 'c'));
  });

  it('should resolve dotfile', () => {
    expect(resolve.sync('.dotfile', { basedir: '', paths: ['/a'], allowZeroBasedPath: true })).toBe(path.join('a', '.dotfile'));
  });

  it('should not resolve . and ..', () => {
    expect(resolve.sync('.', {})).toBe('.');
    expect(resolve.sync('..', {})).toBe('..');
  });

  it('should resolve dotfile in cwd', () => {
    const processCwd = process.cwd;
    process.cwd = () => '/a/b';
    try {
      expect(resolve.sync('.dotfile', {})).toBe(path.join('a', 'b', '.dotfile'));
    } finally {
      process.cwd = processCwd;
    }
  });

  it('should not resolve . and .. with allowZeroBasedPath', () => {
    expect(resolve.sync('.', { allowZeroBasedPath: true })).toBe('.');
    expect(resolve.sync('..', { allowZeroBasedPath: true })).toBe('..');
  });
});