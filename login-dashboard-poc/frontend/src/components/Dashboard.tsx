import React, { useState, useEffect } from "react";
import { useAuth } from "../hooks/useAuth";
import { api } from "../services/api";

interface User {
  id: string;
  username: string;
}

interface DashboardProps {
  // Add any props you might need
}

export const Dashboard: React.FC<DashboardProps> = () => {
  const { token, logout } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await api.get("/dashboard", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        setUsers(response.data);
      } catch (error) {
        if (error.response.status === 401) {
          logout();
        } else {
          setError("Failed to fetch users");
        }
      }
    };
    fetchUsers();
  }, [token, logout]);

  return (
    <div>
      <h1>Dashboard</h1>
      {error ? (
        <p style={{ color: "red" }}>{error}</p>
      ) : (
        <ul>
          {users.map((user) => (
            <li key={user.id}>{user.username}</li>
          ))}
        </ul>
      )}
    </div>
  );
};
