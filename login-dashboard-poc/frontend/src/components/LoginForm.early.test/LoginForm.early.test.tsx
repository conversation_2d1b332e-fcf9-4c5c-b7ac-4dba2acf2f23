import { useAuth } from "../../hooks/useAuth";
import { loginUser } from "../../services/api";
import LoginForm from "../LoginForm";

// frontend/src/components/LoginForm.test.tsx
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import React from "react";

// frontend/src/components/LoginForm.test.tsx
// Mocks
jest.mock("../../hooks/useAuth", () => ({
  useAuth: () => ({
    login: jest.fn(),
  }),
}));

jest.mock("../../services/api", () => ({
  loginUser: jest.fn(),
}));
describe("LoginForm() LoginForm method", () => {
  // Happy Paths
  describe("Happy paths", () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should render the login form with email and password fields and a submit button", () => {
      // This test ensures the form renders all required fields and the button.
      render(<LoginForm onLoginSuccess={jest.fn()} />);
      expect(screen.getByLabelText(/email:/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password:/i)).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: /login/i })
      ).toBeInTheDocument();
    });

    it("should update email and password fields on user input", () => {
      // This test ensures that typing in the fields updates their values.
      render(<LoginForm onLoginSuccess={jest.fn()} />);
      const emailInput = screen.getByLabelText(/email:/i) as HTMLInputElement;
      const passwordInput = screen.getByLabelText(
        /password:/i
      ) as HTMLInputElement;

      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(passwordInput, { target: { value: "password123" } });

      expect(emailInput.value).toBe("<EMAIL>");
      expect(passwordInput.value).toBe("password123");
    });

    it("should call loginUser, login, and onLoginSuccess on successful form submission", async () => {
      // This test ensures the full happy path: loginUser resolves, login and onLoginSuccess are called.
      const mockToken = "mocked-token";
      (loginUser as jest.Mock).mockResolvedValueOnce({
        data: { token: mockToken },
      });
      const mockLogin = jest.fn();
      (useAuth as jest.Mock).mockReturnValue({ login: mockLogin });
      const onLoginSuccess = jest.fn();

      render(<LoginForm onLoginSuccess={onLoginSuccess} />);
      fireEvent.change(screen.getByLabelText(/email:/i), {
        target: { value: "<EMAIL>" },
      });
      fireEvent.change(screen.getByLabelText(/password:/i), {
        target: { value: "password123" },
      });

      fireEvent.click(screen.getByRole("button", { name: /login/i }));

      await waitFor(() => {
        expect(loginUser).toHaveBeenCalledWith(
          "<EMAIL>",
          "password123"
        );
        expect(mockLogin).toHaveBeenCalledWith(mockToken);
        expect(onLoginSuccess).toHaveBeenCalled();
      });
    });

    it("should not display an error message on successful login", async () => {
      // This test ensures that no error message is shown when login is successful.
      (loginUser as jest.Mock).mockResolvedValueOnce({
        data: { token: "token" },
      });
      render(<LoginForm onLoginSuccess={jest.fn()} />);
      fireEvent.change(screen.getByLabelText(/email:/i), {
        target: { value: "<EMAIL>" },
      });
      fireEvent.change(screen.getByLabelText(/password:/i), {
        target: { value: "password123" },
      });
      fireEvent.click(screen.getByRole("button", { name: /login/i }));

      await waitFor(() => {
        expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
      });
    });
  });

  // Edge Cases
  describe("Edge cases", () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should display an error message if loginUser throws with a response error", async () => {
      // This test ensures that an error message is shown if loginUser rejects with a response error.
      const errorMessage = "Invalid credentials";
      (loginUser as jest.Mock).mockRejectedValueOnce({
        response: { data: { message: errorMessage } },
      });

      render(<LoginForm onLoginSuccess={jest.fn()} />);
      fireEvent.change(screen.getByLabelText(/email:/i), {
        target: { value: "<EMAIL>" },
      });
      fireEvent.change(screen.getByLabelText(/password:/i), {
        target: { value: "badpassword" },
      });
      fireEvent.click(screen.getByRole("button", { name: /login/i }));

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });
    });

    it("should display the latest error message if loginUser fails multiple times", async () => {
      // This test ensures that the error message updates if the user submits multiple times with errors.
      const firstError = "First error";
      const secondError = "Second error";
      (loginUser as jest.Mock)
        .mockRejectedValueOnce({ response: { data: { message: firstError } } })
        .mockRejectedValueOnce({
          response: { data: { message: secondError } },
        });

      render(<LoginForm onLoginSuccess={jest.fn()} />);
      fireEvent.change(screen.getByLabelText(/email:/i), {
        target: { value: "<EMAIL>" },
      });
      fireEvent.change(screen.getByLabelText(/password:/i), {
        target: { value: "wrong1" },
      });
      fireEvent.click(screen.getByRole("button", { name: /login/i }));

      await waitFor(() => {
        expect(screen.getByText(firstError)).toBeInTheDocument();
      });

      fireEvent.change(screen.getByLabelText(/email:/i), {
        target: { value: "<EMAIL>" },
      });
      fireEvent.change(screen.getByLabelText(/password:/i), {
        target: { value: "wrong2" },
      });
      fireEvent.click(screen.getByRole("button", { name: /login/i }));

      await waitFor(() => {
        expect(screen.getByText(secondError)).toBeInTheDocument();
      });
    });

    it("should not call login or onLoginSuccess if loginUser throws", async () => {
      // This test ensures that login and onLoginSuccess are not called if loginUser fails.
      (loginUser as jest.Mock).mockRejectedValueOnce({
        response: { data: { message: "Invalid credentials" } },
      });
      const mockLogin = jest.fn();
      (useAuth as jest.Mock).mockReturnValue({ login: mockLogin });
      const onLoginSuccess = jest.fn();

      render(<LoginForm onLoginSuccess={onLoginSuccess} />);
      fireEvent.change(screen.getByLabelText(/email:/i), {
        target: { value: "<EMAIL>" },
      });
      fireEvent.change(screen.getByLabelText(/password:/i), {
        target: { value: "badpassword" },
      });
      fireEvent.click(screen.getByRole("button", { name: /login/i }));

      await waitFor(() => {
        expect(mockLogin).not.toHaveBeenCalled();
        expect(onLoginSuccess).not.toHaveBeenCalled();
      });
    });

    it("should handle empty email and password fields and still call loginUser with empty strings", async () => {
      // This test ensures that submitting with empty fields still calls loginUser with empty strings.
      (loginUser as jest.Mock).mockRejectedValueOnce({
        response: { data: { message: "Fields required" } },
      });

      render(<LoginForm onLoginSuccess={jest.fn()} />);
      fireEvent.click(screen.getByRole("button", { name: /login/i }));

      await waitFor(() => {
        expect(loginUser).toHaveBeenCalledWith("", "");
        expect(screen.getByText("Fields required")).toBeInTheDocument();
      });
    });

    it("should display no error message initially", () => {
      // This test ensures that the error message is not present before any submission.
      render(<LoginForm onLoginSuccess={jest.fn()} />);
      expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
    });

    it("should display error message in red color", async () => {
      // This test ensures that the error message is styled in red.
      const errorMessage = "Invalid credentials";
      (loginUser as jest.Mock).mockRejectedValueOnce({
        response: { data: { message: errorMessage } },
      });

      render(<LoginForm onLoginSuccess={jest.fn()} />);
      fireEvent.change(screen.getByLabelText(/email:/i), {
        target: { value: "<EMAIL>" },
      });
      fireEvent.change(screen.getByLabelText(/password:/i), {
        target: { value: "badpassword" },
      });
      fireEvent.click(screen.getByRole("button", { name: /login/i }));

      await waitFor(() => {
        const errorDiv = screen.getByText(errorMessage);
        expect(errorDiv).toBeInTheDocument();
        expect(errorDiv).toHaveStyle("color: red");
      });
    });
  });
});
