import { useState, useEffect } from "react";
import axios from "axios";

interface AuthContext {
  token: string | null;
  user: any;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  error: string | null;
}

const AuthContext = {
  token: null,
  user: null,
  isLoading: false,
  error: null,
};

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthContext>({
    ...AuthContext,
  });

  useEffect(() => {
    const storedToken = localStorage.getItem("token");
    if (storedToken) {
      axios
        .get("/api/dashboard", {
          headers: {
            Authorization: `Bearer ${storedToken}`,
          },
        })
        .then((response) => {
          setAuthState((prevState) => ({
            ...prevState,
            token: storedToken,
            user: response.data,
            isLoading: false,
          }));
        })
        .catch((error) => {
          setAuthState((prevState) => ({
            ...prevState,
            error: error.message,
            isLoading: false,
          }));
          localStorage.removeItem("token");
        });
    } else {
      setAuthState((prevState) => ({
        ...prevState,
        isLoading: false,
      }));
    }
  }, []);

  const login = async (email: string, password: string) => {
    setAuthState((prevState) => ({
      ...prevState,
      isLoading: true,
      error: null,
    }));
    try {
      const response = await axios.post("/api/auth/login", {
        email,
        password,
      });
      const token = response.data.token;
      localStorage.setItem("token", token);
      axios
        .get("/api/dashboard", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })
        .then((response) => {
          setAuthState((prevState) => ({
            ...prevState,
            token,
            user: response.data,
            isLoading: false,
          }));
        })
        .catch((error) => {
          setAuthState((prevState) => ({
            ...prevState,
            error: error.message,
            isLoading: false,
          }));
          localStorage.removeItem("token");
        });
    } catch (error: any) {
      setAuthState((prevState) => ({
        ...prevState,
        error: error.message,
        isLoading: false,
      }));
    }
  };

  const logout = () => {
    localStorage.removeItem("token");
    setAuthState({
      token: null,
      user: null,
      isLoading: false,
      error: null,
    });
  };

  return { ...authState, login, logout };
};
