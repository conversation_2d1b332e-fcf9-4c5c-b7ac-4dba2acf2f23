import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from "axios";
import { toast } from "react-toastify";

export const api = axios.create({
  baseURL: "http://localhost:8000",
});

const getToken = () => {
  const token = localStorage.getItem("token");
  return token;
};

api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = getToken();
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    if (error.response?.status === 401) {
      localStorage.removeItem("token");
      toast.error("Unauthorized access. Please login again.");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

export const apiService = {
  get: (url: string, config?: AxiosRequestConfig) => api.get(url, config),
  post: (url: string, data: any, config?: AxiosRequestConfig) =>
    api.post(url, data, config),
  put: (url: string, data: any, config?: AxiosRequestConfig) =>
    api.put(url, data, config),
  delete: (url: string, config?: AxiosRequestConfig) => api.delete(url, config),
};
